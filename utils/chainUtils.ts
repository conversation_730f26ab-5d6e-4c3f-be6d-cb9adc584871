import { ethers } from 'ethers';

// Hyperliquid chain configuration
export const HYPERLIQUID_TESTNET = {
  chainId: '0x3e6', // 998 in hex
  chainName: 'Hyperliquid Testnet',
  nativeCurrency: {
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: ['https://api.hyperliquid-testnet.xyz/evm'],
  blockExplorerUrls: ['https://app.hyperliquid.xyz/explorer'],
};

export const HYPERLIQUID_MAINNET = {
  chainId: '0xa4b1', // 42161 in hex (Arbitrum One - placeholder)
  chainName: 'Hyperliquid',
  nativeCurrency: {
    name: 'Ether',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: ['https://api.hyperliquid.xyz/evm'],
  blockExplorerUrls: ['https://app.hyperliquid.xyz/explorer'],
};

// Function to add Hyperliquid network to MetaMask
export const addHyperliquidNetwork = async (isTestnet = true) => {
  if (typeof window === 'undefined' || !window.ethereum) {
    throw new Error('MetaMask is not installed');
  }

  const chainConfig = isTestnet ? HYPERLIQUID_TESTNET : HYPERLIQUID_MAINNET;

  try {
    // Try to switch to the network first
    await window.ethereum.request({
      method: 'wallet_switchEthereumChain',
      params: [{ chainId: chainConfig.chainId }],
    });
  } catch (switchError: any) {
    // If the network doesn't exist, add it
    if (switchError.code === 4902) {
      try {
        await window.ethereum.request({
          method: 'wallet_addEthereumChain',
          params: [chainConfig],
        });
      } catch (addError) {
        throw new Error('Failed to add Hyperliquid network');
      }
    } else {
      throw new Error('Failed to switch to Hyperliquid network');
    }
  }
};

// Function to check if user is on Hyperliquid network
export const isHyperliquidNetwork = async () => {
  if (typeof window === 'undefined' || !window.ethereum) {
    return false;
  }

  try {
    const chainId = await window.ethereum.request({ method: 'eth_chainId' });
    return (
      chainId === HYPERLIQUID_TESTNET.chainId ||
      chainId === HYPERLIQUID_MAINNET.chainId
    );
  } catch (error) {
    return false;
  }
};

// Function to get current chain ID
export const getCurrentChainId = async () => {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null;
  }

  try {
    const chainId = await window.ethereum.request({ method: 'eth_chainId' });
    return parseInt(chainId, 16);
  } catch (error) {
    return null;
  }
};

// Extend existing window.ethereum type
declare global {
  interface Window {
    ethereum?: any;
  }
}
