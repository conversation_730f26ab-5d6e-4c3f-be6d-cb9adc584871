import { ethers } from 'ethers';
import config from '@/config';

// Get the target chain configuration based on environment
export const getTargetChain = () => {
  const isMainnet = config.network === 'hyperliquid-mainnet';
  
  return {
    chainId: isMainnet ? 42161 : 998,
    chainName: isMainnet ? 'Hyperliquid' : 'Hyperliquid Testnet',
    nativeCurrency: {
      name: 'Ether',
      symbol: 'ETH',
      decimals: 18,
    },
    rpcUrls: [isMainnet ? 'https://api.hyperliquid.xyz/evm' : 'https://api.hyperliquid-testnet.xyz/evm'],
    blockExplorerUrls: ['https://app.hyperliquid.xyz/explorer'],
  };
};

// Check if user is on the correct Hyperliquid chain
export const isOnCorrectChain = async (): Promise<boolean> => {
  if (typeof window === 'undefined' || !window.ethereum) {
    return false;
  }

  try {
    const chainId = await window.ethereum.request({ method: 'eth_chainId' });
    const currentChainId = parseInt(chainId, 16);
    const targetChain = getTargetChain();
    
    return currentChainId === targetChain.chainId;
  } catch (error) {
    console.error('Error checking chain:', error);
    return false;
  }
};

// Switch to the correct Hyperliquid chain
export const switchToHyperliquidChain = async (): Promise<boolean> => {
  if (typeof window === 'undefined' || !window.ethereum) {
    throw new Error('MetaMask is not installed');
  }

  const targetChain = getTargetChain();
  const chainIdHex = `0x${targetChain.chainId.toString(16)}`;

  try {
    // Try to switch to the network first
    await window.ethereum.request({
      method: 'wallet_switchEthereumChain',
      params: [{ chainId: chainIdHex }],
    });
    return true;
  } catch (switchError: any) {
    // If the network doesn't exist, add it
    if (switchError.code === 4902) {
      try {
        await window.ethereum.request({
          method: 'wallet_addEthereumChain',
          params: [{
            chainId: chainIdHex,
            chainName: targetChain.chainName,
            nativeCurrency: targetChain.nativeCurrency,
            rpcUrls: targetChain.rpcUrls,
            blockExplorerUrls: targetChain.blockExplorerUrls,
          }],
        });
        return true;
      } catch (addError) {
        console.error('Failed to add Hyperliquid network:', addError);
        throw new Error('Failed to add Hyperliquid network');
      }
    } else {
      console.error('Failed to switch to Hyperliquid network:', switchError);
      throw new Error('Failed to switch to Hyperliquid network');
    }
  }
};

// Get current chain ID
export const getCurrentChainId = async (): Promise<number | null> => {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null;
  }

  try {
    const chainId = await window.ethereum.request({ method: 'eth_chainId' });
    return parseInt(chainId, 16);
  } catch (error) {
    console.error('Error getting chain ID:', error);
    return null;
  }
};

// Listen for chain changes
export const onChainChanged = (callback: (chainId: number) => void) => {
  if (typeof window !== 'undefined' && window.ethereum) {
    const handleChainChanged = (chainId: string) => {
      callback(parseInt(chainId, 16));
    };

    window.ethereum.on('chainChanged', handleChainChanged);

    // Return cleanup function
    return () => {
      if (window.ethereum) {
        window.ethereum.removeListener('chainChanged', handleChainChanged);
      }
    };
  }
  return () => {};
};

// Declare window.ethereum type
declare global {
  interface Window {
    ethereum?: any;
  }
}
