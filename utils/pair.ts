import { BROADCAST_EVENTS } from '@/libs/broadcast';
import { TPair } from '@/types/pair';
import { isZero, minusBN } from './helper';

export const buildTemplateUpdatePairStatsToPairTable = () => {
  return {
    broadcastName: BROADCAST_EVENTS.PAIR_STATS_UPDATED,
    fieldKey: 'pairId',
    formatter: getFormatterForPairStatsChangedEvent,
    update: (oldData: any, newData: any) => {
      // console.log('UpdatePairStatsToPairTable', oldData, 'newData', newData);
      return {
        ...oldData,
        bondingCurve: newData.bondingCurve,
        buyTxns: newData.buyTxns,
        sellTxns: newData.sellTxns,
        totalTxns: newData.totalTxns,
        volumeUsd: newData.volumeUsd,
        liquidity: newData.liquidity,
        liquidityUsd: newData.liquidityUsd,
        marketCapUsd: newData.marketCapUsd,
        priceSui: newData.priceSui,
        priceUsd: newData.priceUsd,
        reserveBase: newData.reserveBase,
        reserveQuote: newData.reserveQuote,
        volume1h: newData.stats.volume['1h'],
        volume24h: newData.stats.volume['24h'],
        marketCap: newData.marketCapUsd,
        stats: {
          ...oldData.stats,
          ...newData,
        },
      };
    },
  };
};
export const getFormatterForPairStatsChangedEvent = (data: any) => {
  return {
    pairId: data.pairId,
    liquidity: data.liquidity,
    liquidityUsd: data.liquidityUsd,
    totalTxns: data.totalTxns,
    buyTxns: data.buyTxns,
    sellTxns: data.sellTxns,
    volumeUsd: data.volumeUsd,
    marketCapUsd: data.marketCapUsd,
    priceUsd: data.priceUsd,
    priceSui: data.priceSui,
    reserveQuote: data.reserveQuote,
    bondingCurve: data.bondingCurve,
    stats: {
      percent: data.percent,
      buyTxn: data.buyTxn,
      sellTxn: data.sellTxn,
      totalNumTxn: data.totalNumTxn,
      volume: data.volume,
    },
  };
};

export const getCirculatingSupply = (pair: TPair) => {
  if (
    pair.tokenBase?.circulatingSupply &&
    !isZero(pair.tokenBase?.circulatingSupply)
  ) {
    return pair.tokenBase?.circulatingSupply;
  }
  if (isZero(pair?.tokenBase?.totalSupply)) {
    return '0';
  }

  const tokenBurned = pair?.tokenBase?.amountBurned || 0;

  if (!isZero(tokenBurned)) {
    return minusBN(
      pair?.tokenBase?.totalSupply,
      tokenBurned,
      // dividedBN(tokenBurned, 10 ** pair?.tokenBase.decimals),
    );
  }
  return pair?.tokenBase?.totalSupply;
};
