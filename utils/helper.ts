/* eslint-disable @typescript-eslint/no-explicit-any */

import config from '@/config';
import { errorMsg, successMsg } from '@/libs/toast';
import BigNumber from 'bignumber.js';
import copy from 'copy-to-clipboard';

export const copyToClipboard = (message: string) => {
  try {
    copy(message);
    successMsg('Copied!');
  } catch (error: any) {
    errorMsg(error.message || 'Something went wrong!');
  }
};

export const getLinkTxExplorer = (digest?: string) => {
  if (!digest) return '#';
  return `${config.explorerUrl}/txblock/${digest}`;
};

export const getLinkAccountExplorer = (address?: string) => {
  if (!address) return '#';
  return `${config.explorerUrl}/account/${address}`;
};

export const multipliedBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber,
  decimals = 15,
) => {
  return new BigNumber(
    new BigNumber(a || 0).multipliedBy(b || 0).toFixed(decimals),
  ).toString();
};

export const dividedBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber,
) => {
  return new BigNumber(a || 0).dividedBy(b || 0).toString();
};

export const isZero = (a: number | string | BigNumber | null | undefined) => {
  return new BigNumber(a || 0).isZero();
};

export const plusBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber,
) => {
  return new BigNumber(a || 0).plus(b || 0).toString();
};

export const minusBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber,
) => {
  return new BigNumber(a || 0).minus(b || 0).toString();
};

export const isMobile = () => {
  const isBrowser = typeof window !== 'undefined';
  return (
    isBrowser &&
    typeof navigator !== 'undefined' &&
    /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent)
  );
};

export const convertDecToMist = (
  value: number | string | BigNumber,
  decimals: number = 9,
) => {
  return new BigNumber(value)
    .multipliedBy(new BigNumber(10).pow(decimals))
    .toString();
};

export const convertMistToDec = (
  value: number | string | BigNumber,
  decimals: number = 9,
) => {
  return new BigNumber(value)
    .dividedBy(new BigNumber(10).pow(decimals))
    .toString();
};

export const getRandomAvatarUrl = () => {
  const styles = ['adventurer', 'avataaars', 'bottts', 'croodles', 'identicon'];
  const randomStyle = styles[Math.floor(Math.random() * styles.length)];
  const randomSeed = Math.random().toString(36).substring(7);
  return `https://api.dicebear.com/7.x/${randomStyle}/svg?seed=${randomSeed}`;
};

export const TELEGRAM_REGEX =
  /^(?:|(https?:\/\/)?(|www)[.]?((t|telegram)\.me)\/)[a-zA-Z0-9_]{5,32}$/gm;

export const WEBSITE_REGEX =
  /^(https?:\/\/)?([\w-]+\.)+[\w-]{2,}(:\d{1,5})?(\/.*)?$/;

export const TWITTER_REGEX =
  /^(https?:\/\/)?(www\.)?(twitter\.com|x\.com)\/[a-zA-Z0-9_]+\/?$/;


export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};
