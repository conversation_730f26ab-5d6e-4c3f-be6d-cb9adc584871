import { SlippageIcon } from '@/assets/icons';
import { NumericFormat } from 'react-number-format';
import { AppButton } from '@/components/AppButton';
import AppModal from '@/components/AppModal';

const SLIPPAGE = [0.5, 1.0, 2.5, 5.0];

export const ModalSettingSlippage = ({
  isOpen,
  onClose,
  slippage,
  setSlippage,
}: {
  isOpen: boolean;
  onClose: () => void;
  slippage: number;
  setSlippage: (value: number) => void;
}) => {
  return (
    <AppModal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center">
          <SlippageIcon className="mr-2" />
          <h2 className="text-font-size-14 text-white-700">Setting slippage</h2>
        </div>
      }
    >
      <div className="mb-4">
        <label htmlFor="slippage" className="text-sm text-white-500 block mb-2">
          Slippage Limit
        </label>
        <NumericFormat
          value={slippage}
          onChange={(e) => setSlippage(parseFloat(e.target.value))}
          thousandSeparator=","
          decimalSeparator="."
          allowNegative={false}
          placeholder="0"
          className="ml-auto text-font-size-12 text-left bg-black-500 w-full appearance-none focus:outline-none p-2"
          allowLeadingZeros={false}
          decimalScale={8}
          inputMode="numeric"
        />
      </div>
      <div
        className="grid grid-cols-4 gap-2 mb-6"
        style={{
          gridTemplateColumns: 'repeat(4, minmax(0, 1fr))',
        }}
      >
        {SLIPPAGE.map((value) => (
          <button
            key={value}
            className={`
                   py-2 rounded-lg text-sm font-medium text-center
                   ${
                     slippage === value
                       ? 'bg-gray-700 text-white'
                       : 'bg-gray-800 text-white-500'
                   }
                   hover:bg-gray-700 hover:text-white
                   `}
            onClick={() => setSlippage(value)}
          >
            {value.toFixed(2)}%
          </button>
        ))}
      </div>
      <AppButton
        className="w-full bg-brand-500 hover:bg-lime-600 text-black-900 font-medium text-sm py-3 rounded-lg"
        onClick={onClose}
        type="submit"
        buttonType="contained-brand"
      >
        Save
      </AppButton>
    </AppModal>
  );
};
