'use client';

import config from '@/config';
import {
  ISocketCoinCreated,
  ISocketCoinUpdated,
  ISocketHolderUpdated,
  ISocketTrades,
} from '@/types';
import { io } from 'socket.io-client';
import { AppBroadcast, BROADCAST_EVENTS } from './broadcast';

declare global {
  interface Window {
    sockets: {
      [ESocketType: string]: ReturnType<typeof io> | null;
    };
  }
}

if (typeof window !== 'undefined') {
  window.sockets = {};
}

export const SOCKET_EVENTS = {
  COIN_CREATED: 'CreatedCoin',
  TRADES: 'CreatedTrade',
  HOLDER_UPDATED: 'UpdatedHolder',
  COIN_UPDATED: 'UpdatedCoin',
};

export const SOCKETS_ROOMS = {
  COIN_DETAIL: (coinAddress: string) => `SUBSCRIBE_COIN::${coinAddress}`,
  MCAP: (coinAddress: string) => `SUBSCRIBE_MCAP::${coinAddress}`,
  NEW_COIN: 'SUBSCRIBE_NEW_COIN',
};

export const socketInstance = (key: string): ReturnType<typeof io> | null =>
  window.sockets[key];

const createInstanceSocket = (socketUrl: string, accessToken?: string) => {
  return io(socketUrl, {
    transports: ['websocket', 'polling'],
    reconnectionDelayMax: 1000,
    autoConnect: true,
    reconnection: true,
    ...(accessToken && { query: { authorization: accessToken } }),
    auth: { offset: undefined },
  });
};

export const closeInstanceSocket = (
  socketInstance: ReturnType<typeof io> | null,
) => {
  if (socketInstance) {
    socketInstance.removeAllListeners();
    socketInstance.close();
  }
};

export const getSocketInstance = (network: string) => {
  if (!network) return null;
  const socketKey = `${network}`?.toUpperCase();
  return socketInstance(socketKey);
};

export const createSocketInstance = ({
  network,
  accessToken,
}: {
  network: string;
  accessToken?: string;
}) => {
  const socketKey = `${network}`?.toUpperCase();
  const socketUrl = config.endpoints.ws;

  if (!socketUrl) {
    console.error('Socket URL is not defined');
    return;
  }

  window.sockets[socketKey] = createInstanceSocket(socketUrl, accessToken);

  window.sockets[socketKey]?.on('connect', () => {
    console.log(
      `Websocket public connection ${network} is connected with server ${socketUrl}`,
    );
    AppBroadcast.dispatch(BROADCAST_EVENTS.SOCKET_CONNECTED, {});

    getSocketInstance(network)?.on(
      SOCKET_EVENTS.COIN_CREATED,
      (data: ISocketCoinCreated) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.COIN_CREATED, data);
      },
    );

    getSocketInstance(network)?.on(
      SOCKET_EVENTS.COIN_UPDATED,
      (data: ISocketCoinUpdated) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.COIN_UPDATED, data);
      },
    );

    getSocketInstance(network)?.on(
      SOCKET_EVENTS.HOLDER_UPDATED,
      (data: ISocketHolderUpdated) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.HOLDER_UPDATED, data);
      },
    );

    getSocketInstance(network)?.on(
      SOCKET_EVENTS.TRADES,
      (data: ISocketTrades) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.TRADES, data);
      },
    );

    subscribeSocketRoom({ network, room: SOCKETS_ROOMS.NEW_COIN });
  });

  window.sockets[socketKey]?.on('disconnect', (reason, details) => {
    console.log(
      `Websocket public connection ${network} is disconnected`,
      reason,
      details,
    );
    AppBroadcast.dispatch(BROADCAST_EVENTS.SOCKET_DISCONNECTED, {});

    unsubscribeSocketRoom({ network, room: SOCKETS_ROOMS.NEW_COIN });
  });
};

export const closeSocketInstance = (network: string) => {
  if (!network) return;
  const socketKey = `${network}`?.toUpperCase();

  closeInstanceSocket(socketInstance(socketKey));
  window.sockets[socketKey] = null;
};

export const subscribeSocketRoom = ({
  network,
  room,
}: {
  network: string;
  room: string;
}) => {
  if (!network) {
    throw new Error('Network is required');
  }

  getSocketInstance(network)?.emit('SUBSCRIBE', {
    room: room,
  });
};

export const unsubscribeSocketRoom = ({
  network,
  room,
}: {
  network: string;
  room: string;
}) => {
  if (!network) {
    throw new Error('Network is required');
  }
  getSocketInstance(network)?.emit('UNSUBSCRIBE', {
    room: room,
  });
};
