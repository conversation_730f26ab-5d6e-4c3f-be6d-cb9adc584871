/* eslint-disable @typescript-eslint/no-explicit-any */

export type TBroadcastEvent = {
  detail: any;
};

export const AppBroadcast = {
  on(event: string, callback: any) {
    document.addEventListener(event, callback);
  },
  dispatch(event: string, data?: any) {
    document.dispatchEvent(new CustomEvent(event, { detail: data }));
  },
  remove(event: string, callback?: any) {
    document.removeEventListener(event, callback);
  },
};

export enum BROADCAST_EVENTS {
  SOCKET_CONNECTED = 'PUBLIC_SOCKET_CONNECTED',
  SOCKET_DISCONNECTED = 'PUBLIC_SOCKET_DISCONNECTED',
  COIN_CREATED = 'COIN_CREATED',
  COIN_UPDATED = 'COIN_UPDATED',
  HOLDER_UPDATED = 'HOLDER_UPDATED',
  TRADES = 'TRADES',
  TRANSACTION_CREATED = 'TRANSACTION_CREATED',
  PAIR_STATS_UPDATED = 'PAIR_STATS_UPDATED',
  REFRESH_DATA = 'REFRESH_DATA',
  LOGOUT = 'LOGOUT',
}
