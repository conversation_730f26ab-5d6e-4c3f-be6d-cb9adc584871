{"address": "7PpY4wSeDXFi1cfKJHgtAhEmHS1DRvkFV8PMBKWrAzZB", "metadata": {"name": "bonding_curve", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "buy", "docs": ["Buys tokens from a bonding curve."], "discriminator": [102, 6, 61, 18, 1, 218, 235, 234], "accounts": [{"name": "user", "docs": ["user"], "writable": true, "signer": true}, {"name": "global", "docs": ["The [Global] to create."], "pda": {"seeds": [{"kind": "const", "value": [103, 108, 111, 98, 97, 108]}]}}, {"name": "fee_recipient", "writable": true}, {"name": "mint"}, {"name": "bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [98, 111, 110, 100, 105, 110, 103, 45, 99, 117, 114, 118, 101]}, {"kind": "account", "path": "mint"}]}}, {"name": "associated_bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "bonding_curve"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "associated_user", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "user"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "amount", "type": "u64"}, {"name": "max_sol_cost", "type": "u64"}]}, {"name": "create", "docs": ["Creates a new coin and bonding curve."], "discriminator": [24, 30, 200, 40, 5, 28, 7, 119], "accounts": [{"name": "user", "docs": ["user"], "writable": true, "signer": true}, {"name": "global", "docs": ["The [Global] to create."], "pda": {"seeds": [{"kind": "const", "value": [103, 108, 111, 98, 97, 108]}]}}, {"name": "mint", "writable": true, "signer": true}, {"name": "bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [98, 111, 110, 100, 105, 110, 103, 45, 99, 117, 114, 118, 101]}, {"kind": "account", "path": "mint"}]}}, {"name": "associated_bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "bonding_curve"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "metadata", "writable": true}, {"name": "mpl_token_metadata", "address": "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "rent", "address": "SysvarRent111111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "name", "type": "string"}, {"name": "symbol", "type": "string"}, {"name": "uri", "type": "string"}]}, {"name": "initialize", "docs": ["Creates the global state."], "discriminator": [175, 175, 109, 31, 13, 152, 155, 237], "accounts": [{"name": "user", "docs": ["user"], "writable": true, "signer": true}, {"name": "global", "docs": ["The [Global] to create."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [103, 108, 111, 98, 97, 108]}]}}, {"name": "system_program", "docs": ["The [System] program."], "address": "11111111111111111111111111111111"}], "args": []}, {"name": "sell", "docs": ["Sells tokens from a bonding curve."], "discriminator": [51, 230, 133, 164, 1, 127, 131, 173], "accounts": [{"name": "user", "docs": ["user"], "writable": true, "signer": true}, {"name": "global", "docs": ["The [Global] to create."], "pda": {"seeds": [{"kind": "const", "value": [103, 108, 111, 98, 97, 108]}]}}, {"name": "fee_recipient", "writable": true}, {"name": "mint"}, {"name": "bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [98, 111, 110, 100, 105, 110, 103, 45, 99, 117, 114, 118, 101]}, {"kind": "account", "path": "mint"}]}}, {"name": "associated_bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "bonding_curve"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "associated_user", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "user"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "amount", "type": "u64"}, {"name": "min_sol_output", "type": "u64"}]}, {"name": "set_params", "docs": ["Sets the global state parameters."], "discriminator": [27, 234, 178, 52, 147, 2, 187, 141], "accounts": [{"name": "user", "docs": ["user"], "writable": true, "signer": true}, {"name": "global", "docs": ["The [Global] to create."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [103, 108, 111, 98, 97, 108]}]}}, {"name": "system_program", "docs": ["The [System] program."], "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "fee_recipient", "type": "pubkey"}, {"name": "initial_virtual_token_reserves", "type": "u64"}, {"name": "initial_virtual_sol_reserves", "type": "u64"}, {"name": "initial_real_token_reserves", "type": "u64"}, {"name": "token_total_supply", "type": "u64"}, {"name": "fee_basis_points", "type": "u64"}]}, {"name": "withdraw", "docs": ["Allows the admin to withdraw liquidity for a migration once the bonding curve complete."], "discriminator": [183, 18, 70, 156, 148, 109, 161, 34], "accounts": [{"name": "user", "docs": ["user"], "writable": true, "signer": true}, {"name": "global", "docs": ["The [Global] to create."], "pda": {"seeds": [{"kind": "const", "value": [103, 108, 111, 98, 97, 108]}]}}, {"name": "mint"}, {"name": "last_withdraw", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [108, 97, 115, 116, 45, 119, 105, 116, 104, 100, 114, 97, 119]}]}}, {"name": "bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [98, 111, 110, 100, 105, 110, 103, 45, 99, 117, 114, 118, 101]}, {"kind": "account", "path": "mint"}]}}, {"name": "associated_bonding_curve", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "bonding_curve"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "associated_user", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "user"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "args": []}], "accounts": [{"name": "BondingCurve", "discriminator": [23, 183, 248, 55, 96, 216, 172, 96]}, {"name": "Global", "discriminator": [167, 232, 232, 177, 200, 108, 114, 127]}, {"name": "LastWithdraw", "discriminator": [203, 18, 220, 103, 120, 145, 187, 2]}], "events": [{"name": "CompleteEvent", "discriminator": [95, 114, 97, 156, 212, 46, 152, 8]}, {"name": "CreateEvent", "discriminator": [27, 114, 169, 77, 222, 235, 99, 118]}, {"name": "SetParamsEvent", "discriminator": [223, 195, 159, 246, 62, 48, 143, 131]}, {"name": "TradeEvent", "discriminator": [189, 219, 127, 211, 78, 230, 97, 238]}], "errors": [{"code": 6000, "name": "NotAuthorized", "msg": "The given account is not authorized to execute this instruction."}, {"code": 6001, "name": "AlreadyInitialized", "msg": "The program is already initialized."}, {"code": 6002, "name": "TooMuchSolRequired", "msg": "slippage: Too much SOL required to buy the given amount of tokens."}, {"code": 6003, "name": "TooLittleSolReceived", "msg": "slippage: Too little SOL received to sell the given amount of tokens."}, {"code": 6004, "name": "MintDoesNotMatchBondingCurve", "msg": "The mint does not match the bonding curve."}, {"code": 6005, "name": "BondingCurveComplete", "msg": "The bonding curve has completed and liquidity migrated to raydium."}, {"code": 6006, "name": "BondingCurveNotComplete", "msg": "The bonding curve has not completed."}, {"code": 6007, "name": "NotInitialized", "msg": "The program is not initialized."}, {"code": 6008, "name": "WithdrawTooFrequent", "msg": "Withdraw too frequent"}, {"code": 6009, "name": "InsufficientTokens", "msg": "Insufficient Tokens"}, {"code": 6010, "name": "InsufficientSOL", "msg": "Insufficient SOL"}, {"code": 6011, "name": "<PERSON><PERSON><PERSON>", "msg": "Min buy is 1 Token"}, {"code": 6012, "name": "Min<PERSON>ell", "msg": "Min sell is 1 Token"}], "types": [{"name": "BondingCurve", "docs": ["A BondingCurve"], "type": {"kind": "struct", "fields": [{"name": "virtual_token_reserves", "docs": ["virtual_token_reserves"], "type": "u64"}, {"name": "virtual_sol_reserves", "docs": ["virtual_sol_reserves"], "type": "u64"}, {"name": "real_token_reserves", "docs": ["real_token_reserves"], "type": "u64"}, {"name": "real_sol_reserves", "docs": ["real_sol_reserves"], "type": "u64"}, {"name": "token_total_supply", "docs": ["token_total_supply"], "type": "u64"}, {"name": "complete", "docs": ["complete"], "type": "bool"}, {"name": "mint", "docs": ["mint"], "type": "pubkey"}]}}, {"name": "CompleteEvent", "docs": ["CompleteEvent"], "type": {"kind": "struct", "fields": [{"name": "user", "docs": ["user"], "type": "pubkey"}, {"name": "mint", "docs": ["mint"], "type": "pubkey"}, {"name": "bonding_curve", "docs": ["bonding_curve"], "type": "pubkey"}, {"name": "timestamp", "docs": ["timestamp"], "type": "i64"}]}}, {"name": "CreateEvent", "docs": ["CreateEvent"], "type": {"kind": "struct", "fields": [{"name": "name", "docs": ["name"], "type": "string"}, {"name": "symbol", "docs": ["symbol"], "type": "string"}, {"name": "uri", "docs": ["uri"], "type": "string"}, {"name": "mint", "docs": ["mint"], "type": "pubkey"}, {"name": "bonding_curve", "docs": ["bonding_curve"], "type": "pubkey"}, {"name": "user", "docs": ["user"], "type": "pubkey"}]}}, {"name": "Global", "docs": ["A Global"], "type": {"kind": "struct", "fields": [{"name": "initialized", "docs": ["initialized"], "type": "bool"}, {"name": "authority", "docs": ["authority"], "type": "pubkey"}, {"name": "fee_recipient", "docs": ["fee_recipient"], "type": "pubkey"}, {"name": "initial_virtual_token_reserves", "docs": ["initial_virtual_token_reserves"], "type": "u64"}, {"name": "initial_virtual_sol_reserves", "docs": ["initial_virtual_token_reserves"], "type": "u64"}, {"name": "initial_real_token_reserves", "docs": ["initial_real_token_reserves"], "type": "u64"}, {"name": "token_total_supply", "docs": ["token_total_supply"], "type": "u64"}, {"name": "fee_basis_points", "docs": ["fee_basis_points"], "type": "u64"}]}}, {"name": "LastWithdraw", "docs": ["A LastWithdraw"], "type": {"kind": "struct", "fields": [{"name": "last_withdraw_timestamp", "type": "i64"}]}}, {"name": "SetParamsEvent", "docs": ["SetParamsEvent"], "type": {"kind": "struct", "fields": [{"name": "fee_recipient", "docs": ["fee_recipient"], "type": "pubkey"}, {"name": "initial_virtual_token_reserves", "docs": ["initial_virtual_token_reserves"], "type": "u64"}, {"name": "initial_virtual_sol_reserves", "docs": ["initial_virtual_token_reserves"], "type": "u64"}, {"name": "initial_real_token_reserves", "docs": ["initial_real_token_reserves"], "type": "u64"}, {"name": "token_total_supply", "docs": ["token_total_supply"], "type": "u64"}, {"name": "fee_basis_points", "docs": ["fee_basis_points"], "type": "u64"}]}}, {"name": "TradeEvent", "docs": ["TradeEvent"], "type": {"kind": "struct", "fields": [{"name": "mint", "docs": ["mint"], "type": "pubkey"}, {"name": "sol_amount", "docs": ["sol_amount"], "type": "u64"}, {"name": "token_amount", "docs": ["token_amount"], "type": "u64"}, {"name": "is_buy", "docs": ["is_buy"], "type": "bool"}, {"name": "user", "docs": ["user"], "type": "pubkey"}, {"name": "timestamp", "docs": ["timestamp"], "type": "i64"}, {"name": "virtual_sol_reserves", "docs": ["virtual_sol_reserves"], "type": "u64"}, {"name": "virtual_token_reserves", "docs": ["virtual_token_reserves"], "type": "u64"}, {"name": "real_sol_reserves", "docs": ["real_sol_reserves"], "type": "u64"}, {"name": "real_token_reserves", "docs": ["real_token_reserves"], "type": "u64"}]}}]}