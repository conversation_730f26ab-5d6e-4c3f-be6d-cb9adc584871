import config from '@/config';
import { BN, Program, Provider, web3 } from '@coral-xyz/anchor';
import {
  createAssociatedTokenAccountInstruction,
  getAssociatedTokenAddress,
} from '@solana/spl-token';
import { Keypair } from '@solana/web3.js';
import { BondingCurve } from './idl/bonding_curve';

const TOKEN_METADATA_PROGRAM_ID = new web3.PublicKey(
  config.tokenMetadataProgramId,
);

export class BondingCurveSDK {
  public readonly connection: web3.Connection;
  public readonly provider: Provider;
  public payerAddress: web3.PublicKey;

  static GLOBAL_SEED = Buffer.from('global');
  static BONDING_CURVE_SEED = Buffer.from('bonding-curve');
  static LAST_WITHDRAW_SEED = Buffer.from('last-withdraw');

  constructor(
    public readonly program: Program<BondingCurve>,
    payerAddress?: web3.PublicKey,
  ) {
    this.connection = this.program.provider.connection;
    this.provider = this.program.provider;
    this.payerAddress = payerAddress ?? this.provider.publicKey!;
  }

  public initializeTx() {
    return this.program.methods.initialize().accounts({
      user: this.payerAddress,
    });
  }

  public setParamsTx(
    feeRecipient: web3.PublicKey,
    initialVirtualTokenReserves: BN,
    initialVirtualSolReserves: BN,
    initialRealTokenReserves: BN,
    tokenTotalSupply: BN,
    feeBasisPoints: BN,
  ) {
    return this.program.methods
      .setParams(
        feeRecipient,
        initialVirtualTokenReserves,
        initialVirtualSolReserves,
        initialRealTokenReserves,
        tokenTotalSupply,
        feeBasisPoints,
      )
      .accounts({
        user: this.payerAddress,
      } as any);
  }

  public createTx(name: string, symbol: string, uri: string, mint: Keypair) {
    const metadata = web3.PublicKey.findProgramAddressSync(
      [
        Buffer.from('metadata'),
        TOKEN_METADATA_PROGRAM_ID.toBuffer(),
        mint.publicKey.toBuffer(),
      ],
      TOKEN_METADATA_PROGRAM_ID,
    )[0];
    return this.program.methods
      .create(name, symbol, uri)
      .accounts({
        user: this.payerAddress,
        metadata,
        mint: mint.publicKey,
      } as any)
      .signers([mint]);
  }

  public async buyTx(
    mint: web3.PublicKey,
    feeRecipient: web3.PublicKey,
    amount: BN,
    maxSolCost: BN,
  ) {
    const associatedUser = await getAssociatedTokenAddress(
      mint,
      this.payerAddress,
    );
    const preInstructions = [];
    if (!(await this.connection.getAccountInfo(associatedUser))) {
      preInstructions.push(
        createAssociatedTokenAccountInstruction(
          this.payerAddress,
          associatedUser,
          this.payerAddress,
          mint,
        ),
      );
    }

    return this.program.methods
      .buy(amount, maxSolCost)
      .accounts({
        user: this.payerAddress,
        mint,
        feeRecipient,
      } as any)
      .preInstructions(preInstructions);
  }

  public sellTx(
    mint: web3.PublicKey,
    feeRecipient: web3.PublicKey,
    amount: BN,
    minSolOutput: BN,
  ) {
    return this.program.methods.sell(amount, minSolOutput).accounts({
      user: this.payerAddress,
      mint,
      feeRecipient,
    } as any);
  }

  public async withdrawTx(mint: web3.PublicKey) {
    const associatedUser = await getAssociatedTokenAddress(
      mint,
      this.payerAddress,
    );
    const preInstructions = [];
    if (!(await this.connection.getAccountInfo(associatedUser))) {
      preInstructions.push(
        createAssociatedTokenAccountInstruction(
          this.payerAddress,
          associatedUser,
          this.payerAddress,
          mint,
        ),
      );
    }

    return this.program.methods
      .withdraw()
      .accounts({
        user: this.payerAddress,
        mint,
      })
      .preInstructions(preInstructions);
  }

  public findGlobalAccount() {
    return web3.PublicKey.findProgramAddressSync(
      [BondingCurveSDK.GLOBAL_SEED],
      this.program.programId,
    );
  }

  public findBondingCurveAccount(mint: web3.PublicKey) {
    return web3.PublicKey.findProgramAddressSync(
      [BondingCurveSDK.BONDING_CURVE_SEED, mint.toBuffer()],
      this.program.programId,
    );
  }

  public findLastWithdraw() {
    return web3.PublicKey.findProgramAddressSync(
      [BondingCurveSDK.LAST_WITHDRAW_SEED],
      this.program.programId,
    );
  }

  public async getBuyPrice(mint: web3.PublicKey, amount: BN) {
    const bondingCurve = this.findBondingCurveAccount(mint)[0];
    const bondingCurveData = await this.program.account.bondingCurve.fetch(
      bondingCurve,
    );

    if (bondingCurveData.complete) {
      throw new Error('Curve is complete');
    }

    // Calculate the product of virtual reserves
    const n = bondingCurveData.virtualSolReserves.mul(
      bondingCurveData.virtualTokenReserves,
    );
    // Calculate the new virtual sol reserves after the purchase
    const i = bondingCurveData.virtualSolReserves.add(amount);

    // Calculate the new virtual token reserves after the purchase
    const r = n.div(i).add(new BN(1));

    // Calculate the amount of tokens to be purchased
    const s = bondingCurveData.virtualTokenReserves.sub(r);

    // Return the minimum of the calculated tokens and real token reserves
    return s < bondingCurveData.realTokenReserves
      ? s
      : bondingCurveData.realTokenReserves;
  }

  public async getSellPrice(mint: web3.PublicKey, amount: BN) {
    const bondingCurve = this.findBondingCurveAccount(mint)[0];
    const bondingCurveData = await this.program.account.bondingCurve.fetch(
      bondingCurve,
    );

    if (bondingCurveData.complete) {
      throw new Error('Curve is complete');
    }

    const globalAccount = this.findGlobalAccount()[0];
    const global = await this.program.account.global.fetch(globalAccount);

    // Calculate the proportional amount of virtual sol reserves to be received
    const n = amount
      .mul(bondingCurveData.virtualSolReserves)
      .div(bondingCurveData.virtualTokenReserves.add(amount));

    // Calculate the fee amount in the same units
    const a = n.mul(global.feeBasisPoints).div(new BN(10000));

    // Return the net amount after deducting the fee
    return n.sub(a);
  }
}
