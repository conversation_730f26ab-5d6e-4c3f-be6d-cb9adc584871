import config from '@/config';
import BaseRequest from './BaseRequest';

export default class CoinRequest extends BaseRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  async createCoin(data: any) {
    const url = '/coin';
    return this.post(url, data);
  }

  async getCoinDetails(coinAddress: string) {
    const url = `/coin/${coinAddress}`;
    return this.get(url);
  }

  async getCoin(params: { page?: number; limit?: number }) {
    const url = '/coin';
    return this.get(url, params);
  }

  async getKingOfTheHill() {
    const url = '/coin/king-of-the-hill';
    return this.get(url);
  }
}
