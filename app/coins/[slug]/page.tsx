'use client';
import {
  BondingCurve,
  CoinInformation,
  ProgressKing,
  StatisticStake,
} from '@/app/coins/[slug]/_components';
import {
  ArrowSwitch,
  CommentIcon,
  InformationCircleIcon,
  StakeIcon,
  TnxIcon,
} from '@/assets/icons';
import CommentBlock from '@/components/comments';
import { HolderList } from '@/components/lists/Holders';
import TransactionList from '@/components/lists/Transactions';
import { OrderForm } from '@/components/orderform';
import TradingView from '@/components/tradingview';
import config from '@/config';
import clsx from 'clsx';
import { notFound } from 'next/navigation';
import { ReactNode, useMemo, useState } from 'react';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { useCoinPageContext } from './provider';
import MainSection from './sections/MainSection';
import RightSection from './sections/RightSection';
import { useMediaQuery } from 'react-responsive';

enum EMobileTabs {
  INFO = 'info',
  THREAD = 'thread',
  TRADE = 'trade',
  TXN = 'txn',
  STAKE = 'stake',
}

interface ITabItem {
  id: EMobileTabs;
  label: ReactNode | string;
  content: ReactNode;
  icon: ReactNode;
}

const CoinPage = () => {
  const { isCoinExist, coin } = useCoinPageContext();
  const [mobileActiveTab, setMobileActiveTab] = useState<EMobileTabs>(
    EMobileTabs.TRADE,
  );

  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  if (!isCoinExist) {
    notFound();
  }

  const mobileTabs: ITabItem[] = useMemo(() => {
    return [
      {
        id: EMobileTabs.INFO,
        label: 'Info',
        icon: <InformationCircleIcon />,
        content: (
          <div>
            <CoinInformation coin={coin} />
            <BondingCurve />
            <ProgressKing />
            <HolderList />
          </div>
        ),
      },
      {
        id: EMobileTabs.TRADE,
        label: 'Trade',
        icon: <ArrowSwitch />,
        content: (
          <div className="h-full flex flex-col">
            <div className="w-full flex-1">
              <TradingView />
            </div>

            <div className="bg-white-50">
              <OrderForm />
            </div>
          </div>
        ),
      },
      {
        id: EMobileTabs.THREAD,
        label: 'Thread',
        icon: <CommentIcon />,
        content: <CommentBlock />,
      },
      {
        id: EMobileTabs.TXN,
        label: 'Txn',
        icon: <TnxIcon />,
        content: <TransactionList />,
      },
      {
        id: EMobileTabs.STAKE,
        label: 'Stake',
        icon: <StakeIcon />,
        content: <StatisticStake coin={coin} />,
      },
    ];
  }, [mobileActiveTab]);

  const _renderDesktopView = () => (
    <div className="border-x border-white-50 w-full flex">
      <div className="hidden desktop:block flex-1 overflow-auto customer-scroll">
        <MainSection />
      </div>
      <div className="hidden desktop:block w-section border-l border-white-50 overflow-auto hide-scroll">
        <RightSection />
      </div>
    </div>
  );

  const _renderMobileView = () => (
    <>
      <div className="block desktop:hidden">
        <div className="h-[calc(100vh-150px)] overflow-auto hide-scroll">
          {mobileTabs.find((tab) => tab.id === mobileActiveTab)?.content}
        </div>
      </div>

      <div className="fixed bottom-0 z-[9999] bg-black-100 flex desktop:hidden w-full border-t border-white-50">
        {mobileTabs.map((tab) => (
          <button
            key={tab.id}
            className={clsx(
              'py-2 px-4 flex-1 text-center group',
              mobileActiveTab === tab.id
                ? 'border-t-[1.5px] border-white-500 text-white-1000 bg-white-100'
                : 'text-white-500 hover:text-white-1000 border-t-[1.5px] border-white-200',
            )}
            onClick={() => setMobileActiveTab(tab.id)}
          >
            <div className="flex flex-col justify-center items-center gap-8px ">
              {tab?.icon}
              <div className="body-sm-medium-12">{tab?.label}</div>
            </div>
          </button>
        ))}
      </div>
    </>
  );

  return (
    <GoogleReCaptchaProvider
      reCaptchaKey={config.recaptchaSiteKey}
      scriptProps={{
        async: false,
        defer: false,
        appendTo: 'head',
        nonce: undefined,
      }}
    >
      {!isMobile ? (
        <div className="hidden desktop:flex pb-[68px] relative mt-[56px] px-[32px]">
          {_renderDesktopView()}
        </div>
      ) : (
        <div className="desktop:hidden relative mt-[48px]">
          {_renderMobileView()}
        </div>
      )}
    </GoogleReCaptchaProvider>
  );
};

export default CoinPage;
