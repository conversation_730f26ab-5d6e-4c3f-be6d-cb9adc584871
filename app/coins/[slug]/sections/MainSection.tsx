import { GroupUserIcon, StarIcon } from '@/assets/icons';
import CommentBlock from '@/components/comments';
import TransactionList from '@/components/lists/Transactions';
import TradingView from '@/components/tradingview';
import clsx from 'clsx';
import React, { useMemo, useState } from 'react';

enum ETabs {
  THREAD = 'investor-relation',
  TRADE = 'trade',
}

interface ITabItem {
  id: ETabs;
  label: React.ReactNode;
  content: React.ReactNode;
}

const MainSection = () => {
  const [activeTab, setActiveTab] = useState<ETabs>(ETabs.THREAD);

  const tabs: ITabItem[] = useMemo(() => {
    const baseTabs: ITabItem[] = [
      {
        id: ETabs.THREAD,
        label: (
          <div className="flex items-center gap-8px">
            <GroupUserIcon
              className={clsx(
                activeTab === ETabs.THREAD
                  ? 'text-white-1000'
                  : 'text-white-500 group-hover:text-white-1000',
              )}
            />
            <p
              className={clsx(
                'text-action-md overflow-hidden text-ellipsis whitespace-nowrap',
                activeTab === ETabs.THREAD ? 'font-semibold' : 'font-medium',
              )}
            >
              Thread
            </p>
          </div>
        ),
        content: <CommentBlock />,
      },
      {
        id: ETabs.TRADE,
        label: (
          <div className="flex items-center gap-8px">
            <StarIcon
              className={clsx(
                activeTab === ETabs.TRADE
                  ? 'text-white-1000'
                  : 'text-white-500 group-hover:text-white-1000',
              )}
            />
            <p
              className={clsx(
                'text-action-md overflow-hidden text-ellipsis whitespace-nowrap',
                activeTab === ETabs.TRADE ? 'font-semibold' : 'font-medium',
              )}
            >
              Trade
            </p>
          </div>
        ),
        content: <TransactionList />,
      },
    ];
    return baseTabs;
  }, [activeTab]);

  return (
    <div className="w-full">
      <div className="w-full h-[467px] lg:w-full border-b-[1.5px] border-white-150">
        <TradingView />
      </div>
      <div className="flex justify-between items-center">
        <div className="flex border-b border-white-50 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={clsx(
                'py-12px px-16px text-center group',
                activeTab === tab.id
                  ? 'border-b-[1.5px] border-white-500 text-white-1000'
                  : 'text-white-500 hover:text-white-1000',
              )}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {tabs.find((tab) => tab.id === activeTab)?.content}
    </div>
  );
};

export default MainSection;
