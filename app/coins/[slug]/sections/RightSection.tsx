import {
  BondingCurve,
  CoinInformation,
  ProgressKing,
  StatisticStake,
} from '@/app/coins/[slug]/_components';
import { HolderList } from '@/components/lists/Holders';
import { OrderForm } from '@/components/orderform';
import { useCoinPageContext } from '../provider';
import React from 'react';

const RightSection: React.FC = () => {
  const { coin } = useCoinPageContext();

  return (
    <div className="shrink-0 w-full">
      <CoinInformation coin={coin} />
      <OrderForm />
      <StatisticStake coin={coin} />
      <BondingCurve />
      <ProgressKing />
      <HolderList />
    </div>
  );
};

export default RightSection;
