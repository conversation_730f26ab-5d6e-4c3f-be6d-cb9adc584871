'use client';

import { KING_THE_HILL } from '@/constants';
import '@/libs/bigNumber';
import BigNumber from '@/libs/bigNumber';
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from '@/libs/broadcast';
import {
  SOCKETS_ROOMS,
  subscribeSocketRoom,
  unsubscribeSocketRoom,
} from '@/libs/socket';
import rf from '@/services/RequestFactory';
import { TCoin } from '@/types';
import { convertMistToDec, dividedBN, multipliedBN } from '@/utils/helper';
import { useParams } from 'next/navigation';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

interface CoinPageContextProps {
  isCoinExist: boolean;
  coinAddress: string;
  coinSymbol: string;
  coin: TCoin;
  coinId: string;
  isCurveComplete: boolean;
  kingTheHillPercent: string;
}

const CoinPageContext = createContext<CoinPageContextProps>({
  isCoinExist: true,
  coin: {} as TCoin,
  coinSymbol: '',
  coinId: '',
  coinAddress: '',
  isCurveComplete: false,
  kingTheHillPercent: '0',
});

const PageProvider = ({ children }: { children: React.ReactNode }) => {
  const { slug: coinAddress }: { slug: string } = useParams();
  const [isCoinExist, setIsCoinExist] = useState(true);
  const [coin, setCoin] = useState<TCoin>({} as TCoin);

  const fetchData = useCallback(async () => {
    try {
      const coinData = await rf
        .getRequest('CoinRequest')
        .getCoinDetails(coinAddress);

      if (!coinData) {
        setIsCoinExist(false);
        return;
      }
      setCoin(coinData);
    } catch (error) {
      setCoin({} as TCoin);
      console.error(`Fetch coin detail error: ${error}`, {
        idOrObjectId: coinAddress,
      });
    }
  }, []);

  const handleWhenCoinUpdated = async (event: TBroadcastEvent) => {
    const data = JSON.parse(event?.detail);
    if (data?.tokenAddress === coinAddress) {
      setCoin((prev) => ({
        ...prev,
        bondingCurve: data?.bondingCurveProgress,
        realSolReserves: data?.realSolReserves,
      }));
    }
  };

  useEffect(() => {
    if (!coinAddress) return;
    fetchData().then();
  }, [coinAddress]);

  useEffect(() => {
    if (!coinAddress) return;
    setTimeout(() => {
      subscribeSocketRoom({
        network: 'sol',
        room: SOCKETS_ROOMS.COIN_DETAIL(coinAddress),
      });
    }, 1000);

    return () => {
      unsubscribeSocketRoom({
        network: 'sol',
        room: SOCKETS_ROOMS.COIN_DETAIL(coinAddress),
      });
    };
  }, [coinAddress]);

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.COIN_UPDATED, handleWhenCoinUpdated);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.COIN_UPDATED, handleWhenCoinUpdated);
    };
  }, []);

  const kingTheHillPercent = useMemo(() => {
    if (!coin?.realSolReserves) return '0';
    const realSolReservesFormatted = convertMistToDec(coin?.realSolReserves);
    return new BigNumber(
      multipliedBN(dividedBN(realSolReservesFormatted, KING_THE_HILL), 100),
    )
      .toFixed(2)
      .toString();
  }, [coin?.realSolReserves]);

  return (
    <CoinPageContext.Provider
      value={{
        coinAddress,
        isCoinExist,
        coin,
        coinSymbol: coin?.symbol,
        coinId: coin?.id,
        kingTheHillPercent,
        isCurveComplete: Number(coin?.bondingCurve || 0) >= 100,
      }}
    >
      {coin?.id ? (
        children
      ) : (
        <div className="text-center text-white-0 flex items-center justify-center h-screen">
          loading...
        </div>
      )}
    </CoinPageContext.Provider>
  );
};

export const useCoinPageContext = () => useContext(CoinPageContext);

export { PageProvider };
