import { TCoin } from '@/types';
import { SolanaIcon } from '@/assets/icons';

export const StatisticStake = ({ coin }: { coin: TCoin }) => {
  return (
    <div className="p-4 border-b border-white-50">
      <div className="border border-white-100 rounded-[4px]">
        <div className="grid grid-cols-3 border-b border-white-100">
          <div className="cursor-pointer flex flex-col gap-1 border-r border-white-100 justify-center items-center py-2 hover:bg-white-100">
            <div className="body-sm-light-12">{coin.symbol} Stakers</div>
            <div className="body-sm-medium-12 text-brand-500 flex gap-1 items-center">
              1,521 <SolanaIcon />
            </div>
          </div>
          <div className="cursor-pointer flex flex-col gap-1 border-r border-white-100 justify-center items-center py-2 hover:bg-white-100">
            <div className="body-sm-light-12 text-white-500">PRINT Stakers</div>
            <div className="body-sm-medium-12 text-brand-500 flex gap-1 items-center">
              1,521 <SolanaIcon />
            </div>
          </div>
          <div className="cursor-pointer flex flex-col gap-1 justify-center items-center py-2 hover:bg-white-100">
            <div className="body-sm-light-12 text-white-500">Creator</div>
            <div className="body-sm-medium-12 text-brand-500 flex gap-1 items-center">
              1,521 <SolanaIcon />
            </div>
          </div>
        </div>

        <div className="p-2">
          <div className="text-white-500 body-sm-light-12 mb-2">
            ${coin.symbol} stakers get 35% of trading fee
          </div>
          <div className="grid grid-cols-4">
            <div className="flex flex-col my-1.5 items-center border-r border-white-100">
              <div className="body-sm-light-12 text-white-300">Total</div>
              <div className="body-sm-medium-12">$1521K</div>
            </div>
            <div className="flex flex-col my-1.5 items-center border-r border-white-100">
              <div className="body-sm-light-12 text-white-300">24h Vol</div>
              <div className="body-sm-medium-12">$15M</div>
            </div>
            <div className="flex flex-col my-1.5 items-center border-r border-white-100">
              <div className="body-sm-light-12 text-white-300">APR</div>
              <div className="body-sm-medium-12">52%</div>
            </div>
            <div className="flex flex-col my-1.5 items-center">
              <div className="body-sm-light-12 text-white-300">My Stake</div>
              <div className="body-sm-medium-12">$1521</div>
            </div>
          </div>
        </div>

        <div className="flex justify-center mb-2 ">
          <div className="w-[80px] py-[5px] text-center cursor-pointer rounded-[4px] text-brand-500 bg-brand-900 body-xs-regular-10">
            Manage
          </div>
        </div>
      </div>
    </div>
  );
};
