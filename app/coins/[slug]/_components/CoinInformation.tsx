import { CopyIcon } from '@/assets/icons';
import { AppNumber } from '@/components/AppNumber';
import { AppTimeDisplay } from '@/components/AppTimeDisplay';
import { SocialCoin } from '@/components/lists/SocialCoin';
import Image from '@/node_modules/next/image';
import { TCoin } from '@/types';
import { formatShortAddress } from '@/utils/format';
import { copyToClipboard } from '@/utils/helper';
import Link from 'next/link';

export const CoinInformation = ({ coin }: { coin: TCoin }) => {
  return (
    <div className="p-4 flex flex-col gap-4 border-b border-white-50">
      <div className="flex items-center justify-between">
        <div className="flex gap-2 items-center">
          <Image
            src={coin.logoUri}
            alt={coin.symbol}
            width={32}
            height={32}
            className="w-32px h-32px rounded-full object-cover"
          />
          <div className="heading-sm-semibold-16 text-brand-500">
            {coin.name}({coin.symbol})
          </div>
          <div className="body-sm-light-12">
            <AppTimeDisplay
              timestamp={new Date(coin?.createdAt).getTime()}
              isAgo
              suffix=""
              classNameWrapper="text-white-500"
            />
          </div>
        </div>

        <SocialCoin coin={coin} />
      </div>

      {coin.description && (
        <div className="truncate line-clamp-[5] text-white-500 body-sm-light-12">
          {coin.description}
        </div>
      )}

      <div className="flex flex-col gap-2">
        <div className="grid-cols-2 grid gap-4 border border-white-100 rounded-[4px] p-2">
          <div className="flex gap-1 body-sm-light-12 text-white-500 pr-4 border-r border-white-100 items-center">
            Creator:{' '}
            <Link
              href={`/profile/${coin.creatorAddress}`}
              className="body-md-regular-14 text-white-0"
            >
              {coin.creatorAddress?.slice(0, 6)}
            </Link>
          </div>
          <div className="flex gap-1 body-sm-light-12 text-white-500 items-center">
            Market cap:{' '}
            <div className="text-brand-500">
              <AppNumber
                value={coin?.mcapUsd}
                isForUSD
                className="body-md-regular-14"
              />
            </div>
          </div>
        </div>
        <div className="border border-white-100 rounded-[4px] p-2 flex items-center justify-between">
          <div className="flex items-center gap-1">
            <div className="body-sm-light-12 text-white-500">
              Contract address:
            </div>
            <div className="body-md-light-14 text-brand-500">
              {formatShortAddress(coin.tokenAddress, 5, 5)}
            </div>
          </div>

          <div
            className="cursor-pointer"
            onClick={() => copyToClipboard(coin.tokenAddress || '')}
          >
            <CopyIcon />
          </div>
        </div>
      </div>
    </div>
  );
};
