'use client';
import { TelegramIcon, TwitterIcon, WebsiteIcon } from '@/assets/icons';
import { SolanaImg } from '@/assets/images';
import { AppButton } from '@/components/AppButton';
import AppInput from '@/components/AppInput';
import AppTextarea from '@/components/AppTextarea';
import { errorMsg, successMsg } from '@/libs/toast';
import rf from '@/services/RequestFactory';
import { TELEGRAM_REGEX, TWITTER_REGEX, WEBSITE_REGEX } from '@/utils/helper';
import Image from 'next/image';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import UploadMedia from './_components/UploadMedia';
import { usePrivy } from '@privy-io/react-auth';

const CreateCoin = () => {
  const { authenticated, login } = usePrivy(); 

  const [file, setFile] = useState<any>();
  const [preview, setPreview] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);

  const { control, handleSubmit, reset } = useForm({
    mode: 'onTouched',
    defaultValues: {
      name: '',
      ticker: '',
      description: '',
      telegram: '',
      website: '',
      twitter: '',
      media: '',
      buyFirst: '',
    },
  });

  const onSubmit = handleSubmit(
    async (data) => {
      if (!file) {
        errorMsg('Please upload a media file');
        return;
      }
      setLoading(true);
      try {
        const mediaForm = new FormData();
        mediaForm.append('media', file);
        mediaForm.append('name', data.name);
        mediaForm.append('symbol', data.ticker);
        mediaForm.append('description', data.description);
        mediaForm.append('telegram', data.telegram || '');
        mediaForm.append('website', data.website || '');
        mediaForm.append('x', data.twitter || '');
        mediaForm.append('buyFirst', data.buyFirst || '');
        const coinRequest = await rf
          .getRequest('CoinRequest')
          .createCoin(mediaForm);
        if (coinRequest) {
          reset();
          setFile(null);
          setPreview(null);
          successMsg('Create coin successfully');
        }
      } catch (error: any) {
        errorMsg(error?.message || 'Something went wrong');
      } finally {
        setLoading(false);
      }
    },
    (error) => {
      console.log('error', error);
      setLoading(false);
    },
  );

  return (
    <div className="relative w-full h-full flex justify-center items-center">
      <div className="hidden md:block absolute top-0 right-0 left-0 bg-[url('../assets/images/banner.png')] aspect-[1440/640] bg-cover z-10"></div>

      <div className="p-6 max-w-[500px] flex justify-center items-top bg-black-800 md:border-[2px] border-white-100 rounded-[4px] z-[15] mt-[20px] mb-[100px] md:my-[100px] backdrop-blur-md">
        <form className="space-y-6" onSubmit={onSubmit}>
          <Controller
            name="name"
            control={control}
            rules={{
              required: 'This field is required.',
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
            }}
            render={({ field, formState }) => (
              <AppInput
                {...field}
                label="Name"
                required
                placeholder="Enter token name here"
                error={formState?.errors?.name?.message}
              />
            )}
          />

          <Controller
            name="ticker"
            control={control}
            rules={{
              required: 'This field is required.',
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
            }}
            render={({ field, formState }) => (
              <AppInput
                {...field}
                label="Ticker"
                required
                placeholder="Enter the token ticker"
                icon={<div className="text-white-0 text-[12px]">$</div>}
                error={formState?.errors?.ticker?.message}
              />
            )}
          />

          <Controller
            name="description"
            control={control}
            rules={{
              required: 'This field is required.',
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
            }}
            render={({ field, formState }) => (
              <AppTextarea
                {...field}
                required
                label="Description"
                placeholder="Enter text here"
                error={formState?.errors?.description?.message}
              />
            )}
          />

          <div className="flex gap-[12px] md:gap-[24px]">
            <div className="w-1/2">
              <UploadMedia
                setFile={setFile}
                preview={preview}
                setPreview={setPreview}
              />
            </div>
            <div className="w-1/2 space-y-4">
              <Controller
                name="website"
                control={control}
                rules={{
                  pattern: {
                    value: WEBSITE_REGEX,
                    message:
                      'Please enter a valid website link (e.g. https://example.com)',
                  },
                }}
                render={({ field, formState }) => (
                  <AppInput
                    {...field}
                    label="Website"
                    placeholder="Enter link"
                    icon={<WebsiteIcon />}
                    error={formState?.errors?.website?.message}
                  />
                )}
              />
              <Controller
                name="twitter"
                control={control}
                rules={{
                  pattern: {
                    value: TWITTER_REGEX,
                    message:
                      'Please enter a valid Twitter or X link (e.g. https://twitter.com/username)',
                  },
                }}
                render={({ field, formState }) => (
                  <AppInput
                    {...field}
                    label="Twitter"
                    placeholder="Enter link"
                    icon={<TwitterIcon />}
                    error={formState?.errors?.twitter?.message}
                  />
                )}
              />
              <Controller
                name="telegram"
                control={control}
                rules={{
                  pattern: {
                    value: TELEGRAM_REGEX,
                    message:
                      'Please enter a valid Telegram link (e.g. https://t.me/username)',
                  },
                }}
                render={({ field, formState }) => (
                  <AppInput
                    {...field}
                    label="Telegram"
                    placeholder="Enter link"
                    icon={<TelegramIcon />}
                    error={formState?.errors?.telegram?.message}
                  />
                )}
              />
            </div>
          </div>

          <div className="w-full h-[1px] bg-white-100"></div>

          <Controller
            name="buyFirst"
            control={control}
            rules={{
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
            }}
            render={({ field, formState }) => (
              <AppInput
                {...field}
                label="Buy First"
                iconPosition="end"
                icon={
                  <div className="text-white-300 text-[12px] flex items-center gap-2 mr-2">
                    0.25
                    <Image
                      src={SolanaImg}
                      width={14}
                      height={14}
                      alt="solana"
                    />
                  </div>
                }
                error={formState?.errors?.buyFirst?.message}
              />
            )}
          />

          {!!authenticated ? (
            <AppButton
              className="w-full bg-brand-500 py-4 !rounded-[2px] hover:scale-[1.02] transition-all duration-300"
              isLoading={loading}
            >
              {loading ? 'Printing...' : 'Print Coin'}
            </AppButton>
          ) : (
            <AppButton
              type="button"
              className="w-full bg-brand-500 py-4 !rounded-[2px] hover:scale-[1.02] transition-all duration-300"
              onClick={() => login()}
            >
              Connect your wallet to print coin
            </AppButton>
          )}
        </form>
      </div>
    </div>
  );
};

export default CreateCoin;
