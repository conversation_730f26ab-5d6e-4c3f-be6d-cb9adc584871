@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'ProtoMono';
  src: local('ProtoMono'),
    url('../assets/fonts/ProtoMono-Regular.otf') format('truetype');
  font-weight: 400;
}

@font-face {
  font-family: 'Inter';
  src: local('Inter-ExtraBold'),
    url('../assets/fonts/Inter-ExtraBold.ttf') format('truetype');
  font-weight: 800;
}

@font-face {
  font-family: 'ProtoMono';
  src: local('ProtoMono'),
    url('../assets/fonts/ProtoMono-Light.otf') format('truetype');
  font-weight: 300;
}

@font-face {
  font-family: 'ProtoMono';
  src: local('ProtoMono'),
    url('../assets/fonts/ProtoMono-Bold.otf') format('truetype');
  font-weight: 700;
}

* {
  font-family: 'ProtoMono', sans-serif;
  font-weight: 400;
}

:root {
  --background: #08090c;
  --foreground: #ffffff;
  color: #08090c;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: sans-serif;
  font-weight: 500;
  font-size: 14px;
}

th {
  @apply px-[8px] py-[6px] text-[12px] font-medium text-left;
}

td {
  @apply px-[8px] py-[10px];
}

.hide-scroll::-webkit-scrollbar {
  display: none;
}

.customer-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.customer-scroll::-webkit-scrollbar {
  width: 2px;
  background: transparent;
  cursor: pointer;
}

.customer-scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

.rc-tooltip-inner {
  padding: 0;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  background: #000 !important;
}

.rc-tooltip {
  opacity: 1 !important;
}

.rc-tooltip-placement-top,
.rc-tooltip-placement-left,
.rc-tooltip-placement-right,
.rc-tooltip-placement-bottom {
  padding: 5px !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
.hide-scroll::-webkit-scrollbar {
  display: none;
}

.customer-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.customer-scroll::-webkit-scrollbar {
  width: 2px;
  background: transparent;
  cursor: pointer;
}

.customer-scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

@layer base {
  .heading-sm-medium-16 {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
  }

  .action-md-semibold-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
  }

  .body-xs-medium-10 {
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .body-sm-medium-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .body-sm-regular-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .body-sm-light-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    line-height: 18px;
  }

  .action-sm-medium-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .body-md-medium-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
  }

  .body-md-semibold-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
  }

  .body-md-regular-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .body-md-light-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: 20px;
  }

  .body-xs-regular-10 {
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
  }

  .body-xs-light-10 {
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: 16px;
  }

  .heading-md-medium-18 {
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-sm-semibold-16 {
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
}

.grecaptcha-badge {
  visibility: hidden !important;
}
