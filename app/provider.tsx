'use client';

import config from '@/config';
import { closeSocketInstance, createSocketInstance } from '@/libs/socket';
import { store } from '@/store';
import { PrivyProvider } from '@privy-io/react-auth';
import { useEffect } from 'react';
import { Provider } from 'react-redux';
import { base } from 'viem/chains';

export default function RootProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const appId = config.privyAppId;
  console.log(config.privyAppId, 'config.privyAppId');
  if (!appId) {
    console.error('Privy app ID is not set');
  }

  useEffect(() => {
    createSocketInstance({ network: 'hyperliquid' });
    return () => {
      closeSocketInstance('hyperliquid');
    };
  }, []);

  return (
    <Provider store={store}>
      <PrivyProvider
        appId={appId}
        config={{
          loginMethods: ['wallet', 'email', 'google', 'twitter'],
          embeddedWallets: {
            createOnLogin: 'users-without-wallets',
          },
          appearance: {
            walletChainType: 'ethereum-only',
            theme: 'dark',
            accentColor: '#676FFF',
          },
          defaultChain:
            config.network === 'hyperliquid-mainnet'
              ? {
                  id: 42161, // Hyperliquid Mainnet
                  name: 'Hyperliquid',
                  network: 'hyperliquid-mainnet',
                  nativeCurrency: {
                    name: 'Ether',
                    symbol: 'ETH',
                    decimals: 18,
                  },
                  rpcUrls: {
                    default: {
                      http: ['https://api.hyperliquid.xyz/evm'],
                    },
                    public: {
                      http: ['https://api.hyperliquid.xyz/evm'],
                    },
                  },
                  blockExplorers: {
                    default: {
                      name: 'Hyperliquid Explorer',
                      url: 'https://app.hyperliquid.xyz/explorer',
                    },
                  },
                }
              : {
                  id: 998, // Hyperliquid Testnet
                  name: 'Hyperliquid Testnet',
                  network: 'hyperliquid-testnet',
                  nativeCurrency: {
                    name: 'Ether',
                    symbol: 'ETH',
                    decimals: 18,
                  },
                  rpcUrls: {
                    default: {
                      http: ['https://api.hyperliquid-testnet.xyz/evm'],
                    },
                    public: {
                      http: ['https://api.hyperliquid-testnet.xyz/evm'],
                    },
                  },
                  blockExplorers: {
                    default: {
                      name: 'Hyperliquid Explorer',
                      url: 'https://app.hyperliquid.xyz/explorer',
                    },
                  },
                },
          supportedChains:
            config.network === 'hyperliquid-mainnet'
              ? [
                  {
                    id: 42161, // Hyperliquid Mainnet
                    name: 'Hyperliquid',
                    network: 'hyperliquid-mainnet',
                    nativeCurrency: {
                      name: 'Ether',
                      symbol: 'ETH',
                      decimals: 18,
                    },
                    rpcUrls: {
                      default: {
                        http: ['https://api.hyperliquid.xyz/evm'],
                      },
                      public: {
                        http: ['https://api.hyperliquid.xyz/evm'],
                      },
                    },
                    blockExplorers: {
                      default: {
                        name: 'Hyperliquid Explorer',
                        url: 'https://app.hyperliquid.xyz/explorer',
                      },
                    },
                  },
                ]
              : [
                  {
                    id: 998, // Hyperliquid Testnet
                    name: 'Hyperliquid Testnet',
                    network: 'hyperliquid-testnet',
                    nativeCurrency: {
                      name: 'Ether',
                      symbol: 'ETH',
                      decimals: 18,
                    },
                    rpcUrls: {
                      default: {
                        http: ['https://api.hyperliquid-testnet.xyz/evm'],
                      },
                      public: {
                        http: ['https://api.hyperliquid-testnet.xyz/evm'],
                      },
                    },
                    blockExplorers: {
                      default: {
                        name: 'Hyperliquid Explorer',
                        url: 'https://app.hyperliquid.xyz/explorer',
                      },
                    },
                  },
                ],
        }}
      >
        {children}
      </PrivyProvider>
    </Provider>
  );
}
