'use client';

import config from '@/config';
import { closeSocketInstance, createSocketInstance } from '@/libs/socket';
import { store } from '@/store';
import { PrivyProvider } from '@privy-io/react-auth';
import { useEffect } from 'react';
import { Provider } from 'react-redux';
import { base } from 'viem/chains';

export default function RootProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const appId = config.privyAppId;
  console.log(config.privyAppId, 'config.privyAppId');
  if (!appId) {
    console.error('Privy app ID is not set');
  }

  useEffect(() => {
    createSocketInstance({ network: 'hyperliquid' });
    return () => {
      closeSocketInstance('hyperliquid');
    };
  }, []);

  return (
    <Provider store={store}>
      <PrivyProvider
        appId={appId}
        config={{
          loginMethods: ['wallet', 'email', 'google', 'twitter'],
          embeddedWallets: {
            createOnLogin: 'users-without-wallets',
          },
          appearance: {
            walletChainType: 'ethereum-only',
            theme: 'dark',
            accentColor: '#676FFF',
          },
          supportedChains: [base],
        }}
      >
        {children}
      </PrivyProvider>
    </Provider>
  );
}
