'use client';

import config from '@/config';
import { closeSocketInstance, createSocketInstance } from '@/libs/socket';
import { store } from '@/store';
import { PrivyProvider } from '@privy-io/react-auth';
import { toSolanaWalletConnectors } from '@privy-io/react-auth/solana';
import { useEffect } from 'react';
import { Provider } from 'react-redux';

export default function RootProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const appId = config.privyAppId;
  if (!appId) {
    console.error('Privy app ID is not set');
  }
  const solanaConnectors = toSolanaWalletConnectors({
    shouldAutoConnect: true,
  });

  useEffect(() => {
    createSocketInstance({ network: 'hypervm' });
    return () => {
      closeSocketInstance('hypervm');
    };
  }, []);

  return (
    <Provider store={store}>
      <PrivyProvider
        appId={appId}
        config={{
          loginMethods: ['wallet', 'email', 'google', 'twitter'],
          embeddedWallets: {
            solana: {
              createOnLogin: 'users-without-wallets', // defaults to 'off'
            },
          },
          appearance: {
            walletChainType: 'solana-only',
            theme: 'dark',
            accentColor: '#676FFF',
          },
          solanaClusters: [
            {
              name: config.network as any,
            },
          ],
          externalWallets: {
            solana: {
              connectors: solanaConnectors,
            },
          },
        }}
      >
        {children}
      </PrivyProvider>
    </Provider>
  );
}
