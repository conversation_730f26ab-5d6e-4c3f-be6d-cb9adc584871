'use client';

import config from '@/config';
import { closeSocketInstance, createSocketInstance } from '@/libs/socket';
import { store } from '@/store';
import { PrivyProvider } from '@privy-io/react-auth';
import { useEffect } from 'react';
import { Provider } from 'react-redux';

export default function RootProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const appId = config.privyAppId;
  if (!appId) {
    console.error('Privy app ID is not set');
  }

  useEffect(() => {
    createSocketInstance({ network: 'hyperliquid' });
    return () => {
      closeSocketInstance('hyperliquid');
    };
  }, []);

  return (
    <Provider store={store}>
      <PrivyProvider
        appId={appId}
        config={{
          loginMethods: ['wallet', 'email', 'google', 'twitter'],
          embeddedWallets: {
            createOnLogin: 'users-without-wallets',
          },
          appearance: {
            walletChainType: 'ethereum-only',
            theme: 'dark',
            accentColor: '#676FFF',
          },
          // For now, let's use standard chains and handle Hyperliquid switching in the app
          defaultChain: {
            id: 1, // Ethereum mainnet as default
            name: 'Ethereum',
            network: 'ethereum',
            nativeCurrency: {
              name: 'Ether',
              symbol: 'ETH',
              decimals: 18,
            },
            rpcUrls: {
              default: {
                http: ['https://eth.llamarpc.com'],
              },
              public: {
                http: ['https://eth.llamarpc.com'],
              },
            },
          },
          supportedChains: [
            {
              id: 1, // Ethereum mainnet
              name: 'Ethereum',
              network: 'ethereum',
              nativeCurrency: {
                name: 'Ether',
                symbol: 'ETH',
                decimals: 18,
              },
              rpcUrls: {
                default: {
                  http: ['https://eth.llamarpc.com'],
                },
                public: {
                  http: ['https://eth.llamarpc.com'],
                },
              },
            },
            {
              id: 11155111, // Sepolia testnet
              name: 'Sepolia',
              network: 'sepolia',
              nativeCurrency: {
                name: 'Ether',
                symbol: 'ETH',
                decimals: 18,
              },
              rpcUrls: {
                default: {
                  http: [
                    'https://sepolia.infura.io/v3/********************************',
                  ],
                },
                public: {
                  http: [
                    'https://sepolia.infura.io/v3/********************************',
                  ],
                },
              },
            },
          ],
        }}
      >
        {children}
      </PrivyProvider>
    </Provider>
  );
}
