import config from '@/config';
import { LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js';
import { useCallback, useEffect, useState } from 'react';
import { useBondingCurveSDK } from './useBondingCurveSDK';
import { usePrivy } from '@privy-io/react-auth';
export const useBalance = () => {
  const { authenticated } = usePrivy();
  const { activeWallet, connection } = useBondingCurveSDK();
  const [solBalance, setSolBalance] = useState<number | null>(null);
  const [tokens, setTokens] = useState<{ mint: string; balance: number }[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchBalances = useCallback(async () => {
    console.log('💔💔💔💔💔--------> fetchBalances', {
      activeWallet,
      authenticated,
    });
    if (!activeWallet || !authenticated) return;
    setIsLoading(true);
    try {
      // Fetch SOL balance
      console.log(
        '💔💔💔💔💔--------> activeWallet?.address',
        activeWallet?.address,
      );
      const publicKey = new PublicKey(activeWallet?.address);
      const balance = await connection.getBalance(publicKey);

      setSolBalance(balance / LAMPORTS_PER_SOL);

      // Fetch SPL token balances
      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
        publicKey,
        {
          programId: new PublicKey(config.tokenProgramId), // SPL Token Program ID
        },
      );

      const formattedTokens = (tokenAccounts.value || []).map(
        ({ account }) => ({
          mint: account.data.parsed.info.mint,
          balance: account.data.parsed.info.tokenAmount.uiAmount || 0,
        }),
      );

      setTokens(formattedTokens);
    } catch (error) {
      console.error('Error fetching balances:', error);
    } finally {
      setIsLoading(false);
    }
  }, [activeWallet?.address, authenticated]);

  const getBalanceByTokenAddress = useCallback(
    (mint: string) => {
      if (!authenticated) return 0;
      return tokens.find((x) => x?.mint === mint)?.balance || 0;
    },
    [tokens, authenticated],
  );

  useEffect(() => {
    if (activeWallet?.address) {
      fetchBalances();
    }
  }, [activeWallet?.address, authenticated]);

  return {
    solBalance,
    tokens,
    isLoading,
    fetchBalances,
    getBalanceByTokenAddress,
  };
};
