import { ethers } from 'ethers';
import { useCallback, useEffect, useState } from 'react';
import { useHyperliquidSDK } from './useHyperliquidSDK';
import { usePrivy } from '@privy-io/react-auth';
export const useBalance = () => {
  const { authenticated } = usePrivy();
  const { activeWallet, provider } = useHyperliquidSDK();
  const [ethBalance, setEthBalance] = useState<number | null>(null);
  const [tokens, setTokens] = useState<
    { address: string; balance: number; symbol: string }[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchBalances = useCallback(async () => {
    console.log('💔💔💔💔💔--------> fetchBalances', {
      activeWallet,
      authenticated,
    });
    if (!activeWallet || !authenticated || !provider) return;
    setIsLoading(true);
    try {
      // Fetch ETH balance
      console.log(
        '💔💔💔💔💔--------> activeWallet?.address',
        activeWallet?.address,
      );
      const address = activeWallet.address;
      const balance = await provider.getBalance(address);
      setEthBalance(parseFloat(ethers.formatEther(balance)));

      // Fetch ERC-20 token balances
      // Note: This is a simplified implementation
      // In a real app, you'd need to query specific token contracts
      const tokenBalances: {
        address: string;
        balance: number;
        symbol: string;
      }[] = [];

      // Example: Query USDC balance (you'd need the actual contract address)
      // const usdcContract = new ethers.Contract(USDC_ADDRESS, ERC20_ABI, provider);
      // const usdcBalance = await usdcContract.balanceOf(address);
      // tokenBalances.push({
      //   address: USDC_ADDRESS,
      //   balance: parseFloat(ethers.formatUnits(usdcBalance, 6)),
      //   symbol: 'USDC'
      // });

      setTokens(tokenBalances);
    } catch (error) {
      console.error('Error fetching balances:', error);
    } finally {
      setIsLoading(false);
    }
  }, [activeWallet?.address, authenticated, provider]);

  const getBalanceByTokenAddress = useCallback(
    (tokenAddress: string) => {
      if (!authenticated) return 0;
      return tokens.find((x) => x?.address === tokenAddress)?.balance || 0;
    },
    [tokens, authenticated],
  );

  useEffect(() => {
    if (activeWallet?.address) {
      fetchBalances();
    }
  }, [activeWallet?.address, authenticated, fetchBalances]);

  return {
    ethBalance,
    tokens,
    isLoading,
    fetchBalances,
    getBalanceByTokenAddress,
  };
};
