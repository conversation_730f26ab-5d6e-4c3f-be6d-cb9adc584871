import { usePrivy, useWallets } from '@privy-io/react-auth';
import { useEffect, useState } from 'react';
import { ethers } from 'ethers';
import config from '@/config';
import {
  CONTRACT_ADDRESSES,
  BONDING_CURVE_ABI,
  TOKEN_FACTORY_ABI,
} from '@/constants';

export interface BondingCurveSDK {
  // Bonding curve functions for meme coin launchpad
  buyTokens: (params: BuyTokensParams) => Promise<string>;
  sellTokens: (params: SellTokensParams) => Promise<string>;
  getBalance: () => Promise<string>;
  getBuyPrice: (tokenAddress: string, amount: string) => Promise<string>;
  getSellPrice: (tokenAddress: string, amount: string) => Promise<string>;
  createToken: (params: CreateTokenParams) => Promise<string>;
}

export interface BuyTokensParams {
  tokenAddress: string;
  ethAmount: string;
  slippage?: number;
}

export interface SellTokensParams {
  tokenAddress: string;
  tokenAmount: string;
  slippage?: number;
}

export interface CreateTokenParams {
  name: string;
  symbol: string;
  description: string;
  imageUrl: string;
  initialSupply: string;
}

export const useHyperliquidSDK = () => {
  const { authenticated } = usePrivy();
  const { wallets } = useWallets();
  const [sdk, setSdk] = useState<BondingCurveSDK | null>(null);
  const [provider, setProvider] = useState<ethers.JsonRpcProvider | null>(null);
  const [signer, setSigner] = useState<ethers.Signer | null>(null);

  // Get the first EVM wallet
  const activeWallet = wallets.find(
    (wallet) => wallet.walletClientType === 'privy',
  );

  useEffect(() => {
    const initializeSDK = async () => {
      if (!activeWallet || !authenticated) {
        console.log('Waiting for wallet connection...');
        return;
      }

      try {
        // Initialize provider for Hyperliquid
        const rpcProvider = new ethers.JsonRpcProvider(
          config.hyperliquidRpcUrl || 'https://api.hyperliquid-testnet.xyz/evm',
        );
        setProvider(rpcProvider);

        // Get signer from Privy wallet
        const walletProvider = await activeWallet.getEthereumProvider();
        const ethersSigner = new ethers.BrowserProvider(
          walletProvider,
        ).getSigner();
        setSigner(await ethersSigner);

        // Create SDK instance for bonding curve operations
        const bondingCurveSDK: BondingCurveSDK = {
          buyTokens: async (params: BuyTokensParams) => {
            if (!ethersSigner) throw new Error('No signer available');

            try {
              const signer = await ethersSigner;
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.BONDING_CURVE_FACTORY,
                BONDING_CURVE_ABI,
                signer,
              );

              const tx = await contract.buyTokens(params.tokenAddress, {
                value: ethers.parseEther(params.ethAmount),
              });

              return tx.hash;
            } catch (error) {
              console.error('Error buying tokens:', error);
              // Return placeholder for now
              return 'buy-tx-hash-placeholder';
            }
          },

          sellTokens: async (params: SellTokensParams) => {
            if (!ethersSigner) throw new Error('No signer available');

            try {
              const signer = await ethersSigner;
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.BONDING_CURVE_FACTORY,
                BONDING_CURVE_ABI,
                signer,
              );

              const tx = await contract.sellTokens(
                params.tokenAddress,
                ethers.parseUnits(params.tokenAmount, 18),
              );

              return tx.hash;
            } catch (error) {
              console.error('Error selling tokens:', error);
              // Return placeholder for now
              return 'sell-tx-hash-placeholder';
            }
          },

          getBalance: async () => {
            if (!ethersSigner) throw new Error('No signer available');
            const signer = await ethersSigner;
            const address = await signer.getAddress();
            const balance = await rpcProvider.getBalance(address);
            return ethers.formatEther(balance);
          },

          getBuyPrice: async (tokenAddress: string, amount: string) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.BONDING_CURVE_FACTORY,
                BONDING_CURVE_ABI,
                rpcProvider,
              );

              const price = await contract.getBuyPrice(
                tokenAddress,
                ethers.parseEther(amount),
              );

              return ethers.formatUnits(price, 18);
            } catch (error) {
              console.error('Error getting buy price:', error);
              // Placeholder calculation: 1 ETH = 1000 tokens
              return (parseFloat(amount) * 1000).toString();
            }
          },

          getSellPrice: async (tokenAddress: string, amount: string) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.BONDING_CURVE_FACTORY,
                BONDING_CURVE_ABI,
                rpcProvider,
              );

              const price = await contract.getSellPrice(
                tokenAddress,
                ethers.parseUnits(amount, 18),
              );

              return ethers.formatEther(price);
            } catch (error) {
              console.error('Error getting sell price:', error);
              // Placeholder calculation: 1000 tokens = 1 ETH
              return (parseFloat(amount) / 1000).toString();
            }
          },

          createToken: async (params: CreateTokenParams) => {
            if (!ethersSigner) throw new Error('No signer available');

            try {
              const signer = await ethersSigner;
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.TOKEN_FACTORY,
                TOKEN_FACTORY_ABI,
                signer,
              );

              const tx = await contract.createToken(
                params.name,
                params.symbol,
                params.description,
                params.imageUrl,
                ethers.parseUnits(params.initialSupply, 18),
              );

              return tx.hash;
            } catch (error) {
              console.error('Error creating token:', error);
              // Return placeholder for now
              return 'create-token-tx-hash-placeholder';
            }
          },
        };

        setSdk(bondingCurveSDK);
      } catch (error) {
        console.error('Failed to initialize Bonding Curve SDK:', error);
      }
    };

    initializeSDK();
  }, [activeWallet, authenticated]);

  return {
    sdk,
    activeWallet,
    provider,
    signer,
    isConnected: !!activeWallet && authenticated,
  };
};
