import { BondingCurveSDK } from '@/libs/sdk';
import { BondingCurve } from '@/libs/sdk/idl/bonding_curve';
import { AnchorProvider, Program, web3 } from '@coral-xyz/anchor';
import {useSolanaWallets } from '@privy-io/react-auth/solana';
import { useEffect, useState } from 'react';
import * as IDL from '../libs/sdk/idl/bonding_curve.json';
import { PublicKey } from '@solana/web3.js';
import config from '@/config';

export const useBondingCurveSDK = () => {
  const [sdk, setSdk] = useState<BondingCurveSDK | null>(null);
  const { wallets } = useSolanaWallets();
  const activeWallet = wallets[0];

  const connection = new web3.Connection(
    web3.clusterApiUrl((config.network as any) || 'devnet'),
  );

  useEffect(() => {
    const initializeProgram = async () => {
      if (!activeWallet?.address) {
        console.log("Waiting for wallet...");
        return;
      }

      try {
        const walletAdapter = {
          publicKey: new PublicKey(activeWallet.address),
          signTransaction: activeWallet.signTransaction,
          signAllTransactions: async (transactions: any) => {
            return Promise.all(transactions.map((tx: any) => activeWallet.signTransaction(tx)));
          },
        };

        const provider = new AnchorProvider(
          connection,
          walletAdapter,
          { commitment: 'confirmed' }
        );
        
        const program = new Program<BondingCurve>(IDL as any, provider);
        const sdk = new BondingCurveSDK(program);
        setSdk(sdk);
        return sdk;
      } catch (err) {
        console.error('Failed to initialize RPS program:', err);
//        toast.error('Failed to initialize game program');
      }
    };

    initializeProgram();
  }, [activeWallet?.address]);

  return {
    sdk,
    activeWallet,
    connection,
  };
};
