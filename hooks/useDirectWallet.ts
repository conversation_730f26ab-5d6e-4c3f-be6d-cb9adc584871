import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

interface DirectWalletState {
  isConnected: boolean;
  address: string | null;
  provider: ethers.BrowserProvider | null;
  signer: ethers.JsonRpcSigner | null;
  chainId: number | null;
}

export const useDirectWallet = () => {
  const [wallet, setWallet] = useState<DirectWalletState>({
    isConnected: false,
    address: null,
    provider: null,
    signer: null,
    chainId: null,
  });

  const [isConnecting, setIsConnecting] = useState(false);

  // Check if wallet is already connected
  useEffect(() => {
    const checkConnection = async () => {
      if (typeof window !== 'undefined' && window.ethereum) {
        try {
          const provider = new ethers.BrowserProvider(window.ethereum);
          const accounts = await provider.listAccounts();
          
          if (accounts.length > 0) {
            const signer = await provider.getSigner();
            const address = await signer.getAddress();
            const network = await provider.getNetwork();
            
            setWallet({
              isConnected: true,
              address,
              provider,
              signer,
              chainId: Number(network.chainId),
            });
          }
        } catch (error) {
          console.error('Error checking wallet connection:', error);
        }
      }
    };

    checkConnection();
  }, []);

  // Listen for account changes
  useEffect(() => {
    if (typeof window !== 'undefined' && window.ethereum) {
      const handleAccountsChanged = (accounts: string[]) => {
        if (accounts.length === 0) {
          // Wallet disconnected
          setWallet({
            isConnected: false,
            address: null,
            provider: null,
            signer: null,
            chainId: null,
          });
        } else {
          // Account changed, reconnect
          connectWallet();
        }
      };

      const handleChainChanged = (chainId: string) => {
        // Reload the page when chain changes to avoid issues
        window.location.reload();
      };

      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);

      return () => {
        if (window.ethereum) {
          window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
          window.ethereum.removeListener('chainChanged', handleChainChanged);
        }
      };
    }
  }, []);

  const connectWallet = async () => {
    if (typeof window === 'undefined' || !window.ethereum) {
      alert('Please install MetaMask or another Ethereum wallet');
      return false;
    }

    setIsConnecting(true);

    try {
      // Request account access
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      const network = await provider.getNetwork();

      setWallet({
        isConnected: true,
        address,
        provider,
        signer,
        chainId: Number(network.chainId),
      });

      return true;
    } catch (error) {
      console.error('Error connecting wallet:', error);
      return false;
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectWallet = () => {
    setWallet({
      isConnected: false,
      address: null,
      provider: null,
      signer: null,
      chainId: null,
    });
  };

  const switchChain = async (chainId: number) => {
    if (typeof window === 'undefined' || !window.ethereum) {
      return false;
    }

    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${chainId.toString(16)}` }],
      });
      return true;
    } catch (error) {
      console.error('Error switching chain:', error);
      return false;
    }
  };

  return {
    ...wallet,
    isConnecting,
    connectWallet,
    disconnectWallet,
    switchChain,
  };
};
