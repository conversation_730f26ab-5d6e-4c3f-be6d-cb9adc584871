// Contract addresses for Hyperliquid EVM
export const CONTRACT_ADDRESSES = {
  // Bonding curve factory contract
  BONDING_CURVE_FACTORY: '0x1234567890123456789012345678901234567890',
  
  // Token factory contract
  TOKEN_FACTORY: '0x2345678901234567890123456789012345678901',
  
  // Fee recipient
  FEE_RECIPIENT: '0x742d35Cc6634C0532925a3b8D4C9db96C4b5Da5e',
};

// Basic ERC-20 ABI for token interactions
export const ERC20_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function decimals() view returns (uint8)',
  'function totalSupply() view returns (uint256)',
  'function balanceOf(address) view returns (uint256)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function allowance(address owner, address spender) view returns (uint256)',
  'function approve(address spender, uint256 amount) returns (bool)',
  'function transferFrom(address from, address to, uint256 amount) returns (bool)',
];

// Bonding curve contract ABI
export const BONDING_CURVE_ABI = [
  'function buyTokens(address tokenAddress) payable returns (uint256)',
  'function sellTokens(address tokenAddress, uint256 tokenAmount) returns (uint256)',
  'function getBuyPrice(address tokenAddress, uint256 ethAmount) view returns (uint256)',
  'function getSellPrice(address tokenAddress, uint256 tokenAmount) view returns (uint256)',
  'function getTokenReserves(address tokenAddress) view returns (uint256)',
  'function getEthReserves(address tokenAddress) view returns (uint256)',
  'function isComplete(address tokenAddress) view returns (bool)',
];

// Token factory contract ABI
export const TOKEN_FACTORY_ABI = [
  'function createToken(string name, string symbol, string description, string imageUrl, uint256 initialSupply) returns (address)',
  'function getTokenInfo(address tokenAddress) view returns (tuple(string name, string symbol, string description, string imageUrl, uint256 totalSupply, address creator, uint256 createdAt))',
  'function getAllTokens() view returns (address[])',
];
