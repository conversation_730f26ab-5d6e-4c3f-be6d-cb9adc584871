# Troubleshooting Privy 403 Error

## Issue: 403 Error when trying to login to Privy

The 403 error when calling `https://auth.privy.io/api/v1/siwe/init` is likely due to one of the following issues:

### 1. **Privy App Configuration**

Your current Privy App ID might be configured for Solana only. You need to:

1. Go to [Privy Dashboard](https://dashboard.privy.io)
2. Select your app (ID: `cm78p8cc203umbz29o9la768g`)
3. Navigate to **Settings** → **Chains**
4. Add Ethereum support:
   - Enable **Ethereum Mainnet**
   - Enable **Sepolia Testnet** (for testing)
5. Update **Login Methods** to include Ethereum wallets

### 2. **Domain Whitelist**

Ensure your domain is whitelisted in Privy:

1. In Privy Dashboard → **Settings** → **Domains**
2. Add your domains:
   - `localhost:3000` (for development)
   - Your production domain
   - Any staging domains

### 3. **Chain Configuration**

Since Hyperliquid is a custom chain, we've temporarily configured Privy to use standard Ethereum chains. The app will:

1. Connect with Ethereum/Sepolia initially
2. Prompt users to switch to Hyperliquid network
3. Handle all transactions on Hyperliquid

### 4. **Alternative Solutions**

If the issue persists, you have several options:

#### Option A: Create New Privy App
1. Create a new Privy app specifically for EVM
2. Configure it with Ethereum support from the start
3. Update the `privyAppId` in your config files

#### Option B: Use WalletConnect + RainbowKit
Replace Privy with WalletConnect for more flexibility:

```bash
npm install @rainbow-me/rainbowkit wagmi viem @tanstack/react-query
```

#### Option C: Use ConnectKit
Another alternative wallet connection library:

```bash
npm install connectkit wagmi viem
```

### 5. **Quick Fix for Development**

For immediate testing, you can:

1. Use a different Privy app ID that's already configured for Ethereum
2. Or temporarily use MetaMask directly without Privy

### 6. **Environment Variables**

Make sure you have the correct environment variables:

```env
NEXT_PUBLIC_PRIVY_APP_ID=your-ethereum-enabled-app-id
NEXT_PUBLIC_ENV=dev
```

### 7. **Network Switching Flow**

The app now includes automatic network detection and switching:

1. Users connect with any Ethereum wallet
2. App detects if they're on Hyperliquid
3. If not, shows a "Switch Network" button
4. Automatically adds Hyperliquid network to MetaMask
5. Switches to Hyperliquid for transactions

### 8. **Testing Steps**

1. Clear browser cache and localStorage
2. Try connecting with MetaMask
3. Check browser console for detailed error messages
4. Verify network requests in Network tab
5. Test with different wallets (MetaMask, WalletConnect, etc.)

### 9. **Contact Privy Support**

If none of the above works:

1. Contact Privy support with your app ID
2. Request Ethereum chain support for your existing app
3. Or request help with custom chain configuration

### 10. **Fallback Implementation**

The current implementation includes fallbacks:

- If Privy fails, users can still connect via MetaMask directly
- Network switching is handled gracefully
- All contract interactions work regardless of the connection method

## Next Steps

1. **Immediate**: Check Privy dashboard settings
2. **Short-term**: Test with a new Privy app if needed
3. **Long-term**: Consider alternative wallet connection libraries for more control
