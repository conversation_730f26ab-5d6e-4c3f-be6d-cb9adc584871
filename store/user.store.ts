import { AppBroadcast, BROADCAST_EVENTS } from '@/libs/broadcast';
import Storage from '@/libs/storage';
import { setAuthorizationToRequest } from '@/utils/auth';
import { NETWORKS } from '@/utils/constants';
import { createSlice } from '@reduxjs/toolkit';
import { JwtPayload, jwtDecode } from 'jwt-decode';
import moment from 'moment';

export type UserState = {
  accessToken: string;
  network: string;
};

const isValidAccessToken = (accessToken: string | undefined) => {
  if (!accessToken) return false;

  let decodedInfo: JwtPayload;

  try {
    decodedInfo = jwtDecode(accessToken);
  } catch (e) {
    console.log('jwt decode error', e);
    return false;
  }

  const currentTime = moment().unix();

  if (!decodedInfo.exp || +decodedInfo.exp < currentTime) {
    console.log('The user JWT is expired');
    return false;
  }

  return true;
};

export const getValidAccessTokenFromStorage = () => {
  const accessToken = Storage.getAccessToken();
  if (!accessToken) return '';

  const isValid = isValidAccessToken(accessToken);

  if (!isValid) {
    Storage.setAccessToken('');
    AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
    return '';
  }

  return accessToken || '';
};

const initialState: UserState = {
  accessToken: getValidAccessTokenFromStorage(),
  network: NETWORKS.SOL,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserAuth: (state, action) => {
      const { accessToken } = action.payload;
      Storage.setAccessToken(accessToken);
      setAuthorizationToRequest(accessToken);
      // const decodeInfo = jwtDecode(accessToken) as any;
      state.accessToken = accessToken;
    },

    clearUser: () => {
      setAuthorizationToRequest(null);
      Storage.setAccessToken('');
      Storage.setRefreshToken('');
      return {
        accessToken: '',
        network: NETWORKS.SOL,
      };
    },
  },
});

export const { setUserAuth, clearUser } = userSlice.actions;

export default userSlice.reducer;
