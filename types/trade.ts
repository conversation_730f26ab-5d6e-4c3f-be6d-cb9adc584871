export type TTradeResponse = {
  docs: TTrades[];
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
  pagingCounter: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  prevPage: null;
  nextPage: null;
};

export type TTrades = {
  id: string;
  slot: number;
  hash: string;
  tokenAddress: string;
  index: number;
  tradeType: string;
  maker: string;
  baseAmount: string;
  quoteAmount: string;
  price: string;
  priceUsd: string;
  volume: string;
  volumeUsd: string;
  timestamp: number;
  virtualSolReserves: string;
  virtualTokenReserves: string;
  realSolReserves: string;
  realTokenReserves: string;
};

export type TTradesRequest = {
  tokenAddress: string;
  page?: number;
  limit?: number;
};
