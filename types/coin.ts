export interface TCoin {
  id: string;
  tokenAddress: string;
  name: string;
  symbol: string;
  description: string;
  logoUri: string;
  socials: Socials;
  creatorAddress: string;
  replyCount: number;
  mcap: string;
  prevMcap: string;
  mcapUsd: string;
  virtualSolReserves: string;
  virtualTokenReserves: string;
  realSolReserves: string;
  realTokenReserves: string;
  createdAt: string;
  updatedAt: string;
  lastTrade: any;
  lastReply: string;
  isKing: boolean;
  bondingCurve: number;
  solReward: string;
}

export interface Socials {
  telegram: string;
  website: string;
  x: string;
}
