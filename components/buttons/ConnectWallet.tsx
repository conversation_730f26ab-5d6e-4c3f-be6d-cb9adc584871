'use client';
import { DownArrIcon } from '@/assets/icons';
import { setUserAuth } from '@/store/user.store';
import { setAuthorizationToRequest } from '@/utils/auth';
import { copyToClipboard } from '@/utils/helper';
import { usePrivy } from '@privy-io/react-auth';
import Tooltip from 'rc-tooltip';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import AppAddress from '../AppAddress';
import { AppButton } from '../AppButton';

const ButtonConnectWallet = () => {
  const dispatch = useDispatch();
  const { login, authenticated, logout, user, getAccessToken } = usePrivy();
  const [visibleTooltip, setVisibleTooltip] = useState(false);

  useEffect(() => {
    if (!authenticated || !user?.id) {
      return;
    }

    const setup = async () => {
      const accessToken = await getAccessToken();
      console.log('💔💔💔💔💔--------> accessToken', accessToken);
      setAuthorizationToRequest(accessToken);
      dispatch(setUserAuth({ accessToken }));
    };
    setup();
  }, [user?.id, authenticated]);

  return (
    <>
      {!authenticated ? (
        <AppButton
          onClick={login}
          className="text-[14px] font-[500] w-[120px] max-md:w-max flex-shrink-0"
        >
          Log In
        </AppButton>
      ) : (
        <Tooltip
          overlay={
            <div>
              <button
                onClick={() => {
                  copyToClipboard(user?.wallet?.address || '');
                  setVisibleTooltip(false);
                }}
                className="block w-full px-4  text-left py-2 pr-4 text-white-900 hover:bg-[#404041] rounded-lg"
              >
                Copy address
              </button>
              <button
                onClick={logout}
                className="block w-full px-4 text-left py-2 pr-4 text-white-900 hover:bg-[#404041] rounded-lg"
              >
                Disconnect
              </button>
            </div>
          }
          styles={{
            body: {
              minWidth: 150,
              backgroundColor: '#08090c',
              border: '1px solid #FFFFFF33',
            },
          }}
          trigger={['click']}
          placement="bottom"
          showArrow={false}
          visible={visibleTooltip}
          onVisibleChange={setVisibleTooltip}
        >
          <AppButton className="flex justify-center items-center max-md:px-2">
            <div className="flex flex-row items-center gap-1 ">
              <div className="mr-4">
                <AppAddress address={user?.wallet?.address ?? ''} />
              </div>
              <DownArrIcon />
            </div>
          </AppButton>
        </Tooltip>
      )}
    </>
  );
};

export default ButtonConnectWallet;
