import {
  DropdownParams,
  IChartingLibraryWidget,
  LibrarySymbolInfo,
  SymbolValueFormatterFormatOptions,
} from '@/libs/charting_library/charting_library';
import { CANDLE_TYPE, CANDLE_UNIT } from './consts';
import { TChartSetting } from './setting';
import { TPair, TPairPrice } from '@/types/pair';
import BigNumber from 'bignumber.js';
import { formatUsdNumber } from '@/utils/format';
import { formatPriceWithSubscriptZeros } from './format';

const STANDARD_TITLE_PAGE = 'Pumpfun';

export const priceFormatterFactory = (
  symbolInfo: LibrarySymbolInfo | null,
  minTick: string,
  chartSetting: TChartSetting,
): any => {
  if (symbolInfo === null) {
    return null;
  }

  if (symbolInfo.format === 'price') {
    return {
      format: (
        price: number,
        signPositive?: SymbolValueFormatterFormatOptions,
      ): string => {
        const fixedNumber = [CANDLE_TYPE.MCAP, CANDLE_TYPE.MCAP_USD].includes(
          chartSetting?.type,
        )
          ? 4
          : 5;
        if (price >= 1000000000) {
          return `${(price / 1000000000).toFixed(fixedNumber)}B`;
        }

        if (price >= 1000000) {
          return `${(price / 1000000).toFixed(fixedNumber)}M`;
        }

        if (price >= 1000) {
          return `${(price / 1000).toFixed(fixedNumber)}K`;
        }
        return formatPriceWithSubscriptZeros(price, signPositive);
      },
    };
  }
  return null; // The default formatter will be used.
};

export const buildOtherTradeParams = ({
  otherTrades,
  setOtherTrades,
  showOtherTradesRef,
}: any) => {
  return {
    title: 'Other Trades',
    tooltip: 'Dev/Snipers/Insiders Trades',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M19.5303 7.96967C19.8232 8.26256 19.8232 8.73744 19.5303 9.03033L12.5303 16.0303C12.3897 16.171 12.1989 16.25 12 16.25C11.8011 16.25 11.6103 16.171 11.4697 16.0303L4.46967 9.03033C4.17678 8.73744 4.17678 8.26256 4.46967 7.96967C4.76256 7.67678 5.23744 7.67678 5.53033 7.96967L12 14.4393L18.4697 7.96967C18.7626 7.67678 19.2374 7.67678 19.5303 7.96967Z" fill="currentColor"/> </svg>`,
    items: [
      {
        title: `${otherTrades.DEV ? '◉' : '○'} Dev Trades`,
        onSelect: () => {
          showOtherTradesRef.current = {
            ...showOtherTradesRef.current,
            DEV: !showOtherTradesRef.current.DEV,
          };
          setOtherTrades(showOtherTradesRef.current);
        },
      },
      {
        title: `${otherTrades.SNIPER ? '◉' : '○'} Snipers Trades`,
        onSelect: () => {
          showOtherTradesRef.current = {
            ...showOtherTradesRef.current,
            SNIPER: !showOtherTradesRef.current.SNIPER,
          };
          setOtherTrades(showOtherTradesRef.current);
        },
      },
      {
        title: `${otherTrades.INSIDER ? '◉' : '○'} Insiders Trades`,
        onSelect: () => {
          showOtherTradesRef.current = {
            ...showOtherTradesRef.current,
            INSIDER: !showOtherTradesRef.current.INSIDER,
          };
          setOtherTrades(showOtherTradesRef.current);
        },
      },
    ],
  } as DropdownParams;
};

export const createSwitchPriceType = (
  tvWidget: IChartingLibraryWidget,
  chartSetting: TChartSetting,
  // pair: TPair,
  handler: (data: any) => void,
) => {
  const button = tvWidget.createButton();
  // const quoteSymbol = pair?.tokenQuote?.symbol;
  const switchTo = chartSetting.unit === CANDLE_UNIT.USD ? 'SOL' : 'USD';
  button.setAttribute('title', `Switch to ${switchTo}`);
  button.addEventListener('click', () => {
    handler({
      ...chartSetting,
      unit:
        chartSetting.unit === CANDLE_UNIT.USD
          ? CANDLE_UNIT.TOKEN_QUOTE
          : CANDLE_UNIT.USD,
    });
  });
  button.textContent = `Switch to ${switchTo}`;
  return button;
};

export const createSwitchPriceOrMcapButton = (
  tvWidget: IChartingLibraryWidget,
  chartSetting: TChartSetting,
  handler: (data: any) => void,
) => {
  const priceMcapButton = tvWidget.createButton();
  priceMcapButton.setAttribute('title', 'Switch to price chart');
  priceMcapButton.addEventListener('click', () => {
    handler({
      ...chartSetting,
      type:
        chartSetting.type === CANDLE_TYPE.PRICE
          ? CANDLE_TYPE.MCAP
          : CANDLE_TYPE.PRICE,
    });
  });
  priceMcapButton.innerHTML =
    `<span ${chartSetting.type === CANDLE_TYPE.PRICE ? 'style="color: rgba(0, 204, 163, 0.9)"' : ''}>Price</span>` +
    ' / ' +
    `<span ${chartSetting.type === CANDLE_TYPE.MCAP ? 'style="color: rgba(0, 204, 163, 0.9)"' : ''}>MCap</span>`;
  return priceMcapButton;
};

export const createShowMyTradesButton = (
  tvWidget: IChartingLibraryWidget,
  currentState: boolean,
  handler: (data: any) => void,
) => {
  const showMyTradesButton = tvWidget.createButton();
  showMyTradesButton.setAttribute(
    'title',
    currentState ? 'Hide My Trades' : 'Show My Trades',
  );
  showMyTradesButton.addEventListener('click', () => {
    handler(!currentState);
  });
  showMyTradesButton.textContent = currentState
    ? 'Hide My Trades'
    : 'Show My Trades';

  return showMyTradesButton;
};

export const createOtherTradesDropdown = (
  tvWidget: IChartingLibraryWidget,
  { otherTrades, setOtherTrades, showOtherTradesRef }: any,
  callback: (data: any) => void,
) => {
  tvWidget
    .createDropdown(
      buildOtherTradeParams({
        otherTrades,
        setOtherTrades,
        showOtherTradesRef,
      }),
    )
    .then((otherTradesApi) => {
      callback(otherTradesApi);
    });
};

export const createAvgLineButton = (
  tvWidget: IChartingLibraryWidget,
  currentState: boolean,
  handler: (data: any) => void,
) => {
  const button = tvWidget.createButton();
  button.setAttribute(
    'title',
    currentState ? 'Hide Avg Price Line' : 'Show Avg Price Line',
  );
  button.addEventListener('click', () => {
    handler(!currentState);
  });
  button.textContent = currentState
    ? 'Hide Avg Price Line'
    : 'Show Avg Price Line';
  return button;
};

export const createMyOrdersButton = (
  tvWidget: IChartingLibraryWidget,
  currentState: boolean,
  handler: (data: any) => void,
) => {
  const button = tvWidget.createButton();
  button.setAttribute(
    'title',
    currentState ? 'Hide Order Lines' : 'Show My Orders',
  );
  button.addEventListener('click', () => {
    handler(!currentState);
  });
  button.textContent = currentState ? 'Hide Order Lines' : 'Show My Orders';
  return button;
};

export const getTitle = (
  pair: TPair,
  pairPrice: TPairPrice,
  chartSetting: TChartSetting,
) => {
  if (!pair) return STANDARD_TITLE_PAGE;
  const { tokenBase, tokenQuote } = pair;
  const baseSymbol = tokenBase?.symbol?.toUpperCase();

  if (!baseSymbol) return STANDARD_TITLE_PAGE;
  const price = pairPrice.price;
  if (new BigNumber(price).isZero()) {
    return `${baseSymbol} | RaidenX`;
  }
  if (chartSetting.unit === CANDLE_UNIT.USD) {
    return `${baseSymbol} ${formatUsdNumber(pairPrice.priceUsd)} USD | ${STANDARD_TITLE_PAGE}`;
  }
  if (chartSetting.unit === CANDLE_UNIT.TOKEN_QUOTE) {
    const quoteSymbol = tokenQuote?.symbol?.toUpperCase();
    return `${baseSymbol} ${formatUsdNumber(pairPrice.price)} ${quoteSymbol} | ${STANDARD_TITLE_PAGE}`;
  }
  return STANDARD_TITLE_PAGE;
};
