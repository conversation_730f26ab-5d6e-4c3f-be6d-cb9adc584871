import {
  ChartingLibraryFeatureset,
  Timezone,
} from '@/libs/charting_library/charting_library';
import { isMobile } from '@/utils/helper';
import { TCoin } from '@/types';
import { CANDLE_TYPE } from '@/components/tradingview/utils/consts';

export type TChartSetting = {
  type: CANDLE_TYPE;
  unit: CANDLE_UNIT;
};

export const DEFAULT_TRADING_VIEW_INTERVAL = '1S';

export const DISABLED_FEATURES: ChartingLibraryFeatureset[] = [
  'header_symbol_search',
  // 'header_undo_redo',
  'header_compare',
  // 'display_market_status',
  // 'edit_buttons_in_legend',
  // 'volume_force_overlay',
  // 'legend_context_menu',
  // 'header_widget',
  'symbol_search_hot_key',
  'header_quick_search',
  ...(isMobile() ? ['timeframes_toolbar' as ChartingLibraryFeatureset] : []),
];

export const ENABLED_FEATURES: ChartingLibraryFeatureset[] = [
  'seconds_resolution',
  ...(isMobile()
    ? ['iframe_loading_compatibility_mode' as ChartingLibraryFeatureset]
    : []),
]; //'seconds_resolution', 'study_templates'

export const DATAFEED_CONFIGURATION = {
  supports_search: true,
  supports_marks: true,
  intraday_multipliers: [
    '1S',
    '1',
    '3',
    '5',
    '15',
    '30',
    '60',
    '120',
    '240',
    '360',
    '480',
    '720',
  ],
  supported_resolutions: [
    '1S',
    '1',
    '3',
    '5',
    '15',
    '30',
    '60',
    '120',
    '240',
    '360',
    '480',
    '720',
    '1D',
    '3D',
    '1W',
    '1M',
  ],
};

export enum CANDLE_UNIT {
  USD = 'USD',
  TOKEN_QUOTE = 'TOKEN_QUOTE',
}

export enum SYMBOL_TYPE {
  stock = 'stock',
  bitcoin = 'bitcoin',
}

export const LIST_RESOLUTION_METADATA: {
  [key: string]: {
    key: string;
    value: string;
    valueCompare: number;
    status: boolean;
  };
} = {
  '1S': { key: '1s', value: '1S', valueCompare: 1 / 60, status: true },
  '1m': { key: '1m', value: '1', status: true, valueCompare: 1 },
  '1': { key: '1m', value: '1', status: true, valueCompare: 1 },
  '3m': { key: '3m', value: '3', status: false, valueCompare: 3 },
  '3': { key: '3m', value: '3', status: false, valueCompare: 3 },
  '5m': { key: '5m', value: '5', status: true, valueCompare: 5 },
  '5': { key: '5m', value: '5', status: true, valueCompare: 5 },
  '15m': { key: '15m', value: '15', status: true, valueCompare: 15 },
  '15': { key: '15m', value: '15', status: true, valueCompare: 15 },
  '30m': {
    key: '30m',
    value: '30',
    status: false,
    valueCompare: 30,
  },
  '30': {
    key: '30m',
    value: '30',
    status: false,
    valueCompare: 30,
  },
  '1h': {
    key: '1h',
    value: '60',
    status: true,
    valueCompare: 60,
  },
  '60': {
    key: '1h',
    value: '60',
    status: true,
    valueCompare: 60,
  },
  '2h': {
    key: '2h',
    value: '2h',
    status: false,
    valueCompare: 120,
  },
  '120': {
    key: '2h',
    value: '2h',
    status: false,
    valueCompare: 120,
  },
  '4h': { key: '4h', value: '240', status: true, valueCompare: 240 },
  '240': { key: '4h', value: '240', status: true, valueCompare: 240 },
  '6h': { key: '6h', value: '360', status: false, valueCompare: 360 },
  '360': { key: '6h', value: '360', status: false, valueCompare: 360 },
  '8h': { key: '8h', value: '480', status: false, valueCompare: 480 },
  '480': { key: '8h', value: '480', status: false, valueCompare: 480 },
  '12h': { key: '12h', value: '720', status: false, valueCompare: 720 },
  '720': { key: '12h', value: '720', status: false, valueCompare: 720 },
  '1D': { key: '1D', value: '1D', status: true, valueCompare: 1440 },
  D: { key: '3D', value: '3D', status: false, valueCompare: 4320 }, // it should be '3D', but there is a bug of TradingView, it call get bars with resolution D
  '3D': { key: '3D', value: '3D', status: false, valueCompare: 4320 },
  '1W': { key: '1W', value: '1W', status: false, valueCompare: 10080 },
  '1M': { key: '1M', value: '1M', status: false, valueCompare: 43200 },
};

export const LIST_RESOLUTION_VALUE_LOOKUP_MAP = Object.values(
  LIST_RESOLUTION_METADATA,
).reduce((acc: any, item) => {
  acc[item.value] = item;
  return acc;
}, {});

export const DEFAULT_INTERVAL = '1S';

export const DEFAULT_INTERVAL_METADATA =
  LIST_RESOLUTION_METADATA[DEFAULT_INTERVAL];

export const DEFAULT_LIST_INTERVAL_METADATA = [
  LIST_RESOLUTION_METADATA['1S'],
  LIST_RESOLUTION_METADATA['1m'],
  LIST_RESOLUTION_METADATA['5m'],
  LIST_RESOLUTION_METADATA['15m'],
  LIST_RESOLUTION_METADATA['1h'],
  LIST_RESOLUTION_METADATA['4h'],
  LIST_RESOLUTION_METADATA['1D'],
];

export const intervalMasterArr = [
  {
    row: [
      LIST_RESOLUTION_METADATA['1S'],
      LIST_RESOLUTION_METADATA['1m'],
      LIST_RESOLUTION_METADATA['3m'],
      LIST_RESOLUTION_METADATA['5m'],
    ],
  },
  {
    row: [
      LIST_RESOLUTION_METADATA['15m'],
      LIST_RESOLUTION_METADATA['30m'],
      LIST_RESOLUTION_METADATA['1h'],
      LIST_RESOLUTION_METADATA['2h'],
    ],
  },
  {
    row: [
      LIST_RESOLUTION_METADATA['4h'],
      LIST_RESOLUTION_METADATA['6h'],
      LIST_RESOLUTION_METADATA['8h'],
      LIST_RESOLUTION_METADATA['12h'],
    ],
  },
  {
    row: [
      LIST_RESOLUTION_METADATA['1D'],
      LIST_RESOLUTION_METADATA['3D'],
      LIST_RESOLUTION_METADATA['1W'],
      LIST_RESOLUTION_METADATA['1M'],
    ],
  },
];

export const SI = [
  { value: 1, symbol: '' },
  { value: 1e3, symbol: 'K' },
  { value: 1e6, symbol: 'M' },
  { value: 1e9, symbol: 'B' },
  { value: 1e12, symbol: 't' },
  { value: 1e15, symbol: 'q' },
  { value: 1e18, symbol: 'Q' },
  { value: 1e21, symbol: 's' },
  { value: 1e24, symbol: 'S' },
  { value: 1e27, symbol: 'o' },
  { value: 1e30, symbol: 'n' },
  { value: 1e33, symbol: 'd' },
  { value: 1e36, symbol: 'U' },
  { value: 1e39, symbol: 'D' },
  { value: 1e42, symbol: 'T' },
  { value: 1e45, symbol: 'Qt' },
  { value: 1e48, symbol: 'Qd' },
  { value: 1e51, symbol: 'Sd' },
  { value: 1e54, symbol: 'St' },
  { value: 1e57, symbol: 'O' },
  { value: 1e60, symbol: 'N' },
  { value: 1e63, symbol: 'v' },
  { value: 1e66, symbol: 'c' },
];

export function getClientTimezone(): Timezone {
  const chartProperties = localStorage.getItem('tradingview.chartproperties');
  if (chartProperties) {
    try {
      const properties = JSON.parse(chartProperties);
      if (properties.timezone) {
        return properties.timezone as Timezone;
      }
    } catch (e) {
      console.error('Error getting client timezone', e);
    }
  }

  const timezones: { [key: string]: number } = {
    'America/New_York': -5,
    'America/Los_Angeles': -8,
    'America/Chicago': -6,
    'America/Phoenix': -7,
    'America/Toronto': -5,
    'America/Vancouver': -8,
    'America/Argentina/Buenos_Aires': -3,
    'America/El_Salvador': -6,
    'America/Sao_Paulo': -3,
    'America/Bogota': -5,
    'America/Caracas': -4,
    'Europe/Moscow': 3,
    'Europe/Athens': 2,
    'Europe/Belgrade': 1,
    'Europe/Berlin': 1,
    'Europe/London': 0,
    'Europe/Luxembourg': 1,
    'Europe/Madrid': 1,
    'Europe/Paris': 1,
    'Europe/Rome': 1,
    'Europe/Warsaw': 1,
    'Europe/Istanbul': 3,
    'Europe/Zurich': 1,
    'Australia/Sydney': 10,
    'Australia/Brisbane': 10,
    'Australia/Adelaide': 9.5,
    'Australia/ACT': 10,
    'Asia/Almaty': 6,
    'Asia/Ashkhabad': 5,
    'Asia/Tokyo': 9,
    'Asia/Taipei': 8,
    'Asia/Singapore': 8,
    'Asia/Shanghai': 8,
    'Asia/Seoul': 9,
    'Asia/Tehran': 3.5,
    'Asia/Dubai': 4,
    'Asia/Kolkata': 5.5,
    'Asia/Hong_Kong': 8,
    'Asia/Bangkok': 7,
    'Asia/Chongqing': 8,
    'Asia/Jerusalem': 2,
    'Asia/Kuwait': 3,
    'Asia/Muscat': 4,
    'Asia/Qatar': 3,
    'Asia/Riyadh': 3,
    'Pacific/Auckland': 12,
    'Pacific/Chatham': 12.75,
    'Pacific/Fakaofo': 13,
    'Pacific/Honolulu': -10,
    'America/Mexico_City': -6,
    'Africa/Cairo': 2,
    'Africa/Johannesburg': 2,
    'Asia/Kathmandu': 5.75,
    'US/Mountain': -7,
  };

  const timezone = (new Date().getTimezoneOffset() * -1) / 60;
  for (const key in timezones) {
    if (timezones[key] == timezone) {
      return key as Timezone;
    }
  }
  return 'Etc/UTC';
}

export const getSymbolFromPair = (coin: TCoin, isMobile?: boolean) => {
  if (!coin) {
    return 'N/A';
  }

  const { symbol } = coin;

  if (!symbol) {
    return 'N/A';
  }

  if (isMobile) {
    return `${symbol || 'Unknown'}`?.toUpperCase();
  }

  return `${symbol || 'Unknown'}/SOL`?.toUpperCase();
};

export const formatSymbolPair = (symbol: string, isMobile?: boolean) => {
  if (!symbol) {
    return 'N/A';
  }

  if (isMobile) {
    return `${symbol || 'Unknown'}`?.toUpperCase();
  }

  return `${symbol || 'Unknown'}/SOL`?.toUpperCase();
};

export const formatDescriptionPair = (symbol: string, isMobile?: boolean) => {
  if (!symbol) {
    return 'N/A';
  }

  if (isMobile) {
    return `${symbol || 'Unknown'}`?.toUpperCase();
  }

  return `${symbol || 'Unknown'}/SOL`?.toUpperCase();
};

export const getChartDescription = (coin: TCoin, isMobile?: boolean) => {
  if (!coin) {
    return '';
  }
  const symbol = getSymbolFromPair(coin, isMobile);

  if (isMobile) {
    return symbol;
  }

  return `${symbol}`;
};
