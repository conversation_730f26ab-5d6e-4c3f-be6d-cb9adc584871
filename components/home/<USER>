import { KingIcon, SolanaIcon } from '@/assets/icons';
import { LightningImg } from '@/assets/images';
import { AppNumber } from '@/components/AppNumber';
import { AppTimeDisplay } from '@/components/AppTimeDisplay';
import Link from '@/node_modules/next/link';
import rf from '@/services/RequestFactory';
import { TCoin } from '@/types';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import AppProgressBar from '../AppProgressBar';
import { SocialCoin } from '../lists/SocialCoin';

const CardKingOfTheHill = () => {
  const [kingOfTheHill, setKingOfTheHill] = useState<TCoin | undefined>();

  useEffect(() => {
    const fetchKingOfTheHill = async () => {
      const result = await rf.getRequest('CoinRequest').getKingOfTheHill();
      setKingOfTheHill(result);
    };
    fetchKingOfTheHill().then();
  }, []);

  return !!kingOfTheHill ? (
    <div
      className="rounded-[4px] border-[2px] border-[#8BD961] tablet:p-4 p-2 w-[343px] h-[343px] tablet:w-[425px] tablet:h-[425px] flex items-end"
      style={{
        background: `linear-gradient(180deg, rgba(0, 0, 0, 0.00) 49.88%, #000 101.65%), url(${kingOfTheHill.logoUri})`,
        boxShadow: '0px 0px 150px var(--16, 16px) rgba(39, 217, 113, 0.20)',
      }}
    >
      <div
        className="rounded-[4px] p-2 border border-white-100 bg-white-50 w-full"
        style={{
          boxShadow: '0px 4px 30px 28px rgba(0, 0, 0, 0.25)',
          backdropFilter: 'blur(10px)',
        }}
      >
        <div className="flex justify-between items-center pb-[6px] border-b w-full mb-[6px] border-white-100">
          <div className="flex gap-1">
            <div className="heading-md-medium-18 text-brand-500">
              {kingOfTheHill?.name} (${kingOfTheHill?.symbol})
            </div>
            {kingOfTheHill?.isKing && <KingIcon />}
            <Image
              src={LightningImg}
              width={16}
              height={16}
              alt="lightning"
              className="w-4 h-4"
            />
            <div className="body-sm-regular-12">
              <AppTimeDisplay
                timestamp={new Date(kingOfTheHill?.createdAt).getTime()}
                isAgo
                suffix=""
                classNameWrapper="text-white-0"
              />
            </div>
          </div>
          <SocialCoin coin={kingOfTheHill} />
        </div>
        <div className="grid grid-cols-2">
          <div className="flex flex-col gap-1 pr-3 border-r border-white-100">
            <div className="flex gap-1">
              <div className="body-sm-light-12 text-white-500">Creator:</div>
              <Link
                className="underline"
                href={`/profile/${kingOfTheHill?.creatorAddress}`}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="body-md-regular-14">
                  {kingOfTheHill?.creatorAddress.slice(0, 6)}
                </div>
              </Link>
            </div>
            <div className="flex gap-1">
              <div className="body-sm-light-12 text-white-500">Sol reward:</div>
              <div className="body-md-regular-14 flex items-center gap-1">
                {kingOfTheHill?.solReward} <SolanaIcon />
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-1 pl-3">
            <div className="flex gap-1">
              <div className="body-sm-light-12 text-white-500">Holders:</div>
              <div className="body-md-regular-14 text-brand-500">1500</div>
            </div>
            <div className="flex gap-1">
              <div className="body-sm-light-12 text-white-500">Market cap:</div>
              <div className="text-brand-500">
                <AppNumber
                  value={kingOfTheHill?.mcapUsd}
                  isForUSD
                  className="body-md-regular-14"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="pt-[6px] border-t w-full mt-[6px] border-white-100">
          <div className="body-sm-regular-12 text-white-500 flex gap-2 item-center pt-1">
            Bounding curve:
            <div className="body-md-regular-14 text-brand-500">
              {kingOfTheHill?.bondingCurve}%
            </div>
          </div>
          <AppProgressBar percent={kingOfTheHill?.bondingCurve} />
        </div>
      </div>
    </div>
  ) : null;
};

export const BannerMobile = () => {
  return (
    <div className="flex justify-center items-center flex-col mb-[24px]">
      <div className="bg-[url('../assets/images/banner.png')] aspect-[375/167] w-full bg-contain bg-no-repeat flex flex-col items-center justify-center">
        <div className="pt-[48px]">
          <div className="text-[32px] leading-[1.2] font-bold mb-4 font-[Inter]">
            Print Meme Earn SOL
          </div>
        </div>

        <div className="flex gap-2 items-center">
          <Link href="/coins/create">
            <div className="text-black-900 p-2 body-sm-regular-12 rounded-[4px] bg-brand-500 cursor-pointer">
              Print a New Meme
            </div>
          </Link>
          <div className="body-sm-regular-12">Or</div>
          <div className="text-black-900 p-2 body-sm-regular-12 rounded-[4px] bg-white-1000 cursor-pointer">
            Learn more
          </div>
        </div>
      </div>

      <CardKingOfTheHill />
    </div>
  );
};

export const Banner = () => {
  const isMobile = useMediaQuery({ query: '(max-width: 992px)' });

  if (isMobile) {
    return <BannerMobile />;
  }

  return (
    <div className="bg-[url('../assets/images/banner.png')] w-full bg-center aspect-[1440/640] min-h-[640px] h-full bg-cover flex justify-center items-center gap-[56px]">
      <CardKingOfTheHill />
      <div className="max-w-[520px]">
        <div className="text-[64px] mb-[56px] font-[Inter] leading-[1.2]">
          Print Meme <br /> Earn SOL
        </div>
        <div className="flex gap-4">
          <Link href="/coins/create">
            <div className="text-black-900 p-4 body-md-semibold-14 rounded-[2px] bg-brand-500 cursor-pointer">
              Print a New Meme
            </div>
          </Link>

          <div className="text-black-900 p-4 body-md-semibold-14 rounded-[2px] bg-white-1000 cursor-pointer">
            Learn more
          </div>
        </div>
      </div>
    </div>
  );
};
