import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { CoinTip, FlashIcon, SettingIcon, SlippageIcon } from '@/assets/icons';
import { AppButton } from '@/components/AppButton';
import config from '@/config';
import { SOL_DECIMALS } from '@/constants';
import { useBalance } from '@/hooks';
import { useBondingCurveSDK } from '@/hooks/useBondingCurveSDK';
import { errorMsg, successMsg } from '@/libs/toast';
import { ModalSettingSlippage } from '@/modals';
import { convertDecToMist, truncateDecimals } from '@/utils/format';
import { BN } from '@coral-xyz/anchor';
import { PublicKey } from '@solana/web3.js';
import BigNumber from 'bignumber.js';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';

const PERCENT_SELL = [10, 20, 50, 100];

const SellOrderForm = () => {
  const { sdk, activeWallet, connection } = useBondingCurveSDK();

  const { getBalanceByTokenAddress, fetchBalances } = useBalance();
  const [isOpenModalSettingSlippage, setIsOpenModalSettingSlippage] =
    useState<boolean>(false);
  const [sellAmount, setSellAmount] = useState<string>('');
  const [slippage, setSlippage] = useState<number>(5.0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [estimatedAmountOut, setEstimateAmountOut] = useState('');

  const { coin, coinAddress, isCurveComplete } = useCoinPageContext();

  const balance = getBalanceByTokenAddress(coinAddress);

  const setPercentageOfBalance = (balance: string, percentage: number) => {
    if (!balance) return;
    const percentageOfBalance = (Number(balance) * percentage) / 100;
    setSellAmount(truncateDecimals(percentageOfBalance, 6).toString());
  };

  console.log(estimatedAmountOut, 'estimatedAmountOut');

  useEffect(() => {
    setEstimateAmountOut('');
    if (!sellAmount || !sdk || isCurveComplete) {
      return;
    }
    const getEstimateAmountOut = async () => {
      const res = await sdk.getSellPrice(
        new PublicKey(coinAddress),
        new BN(convertDecToMist(sellAmount, 6)),
      );
      if (Number(res) > 0) setEstimateAmountOut(res.toString());
    };
    getEstimateAmountOut();
  }, [sellAmount, sdk]);

  const handleSellCoin = async () => {
    if (!sdk) return;
    if (Number(sellAmount) <= 0) {
      errorMsg('Amount must be greater than 0');
      return;
    }
    if (new BigNumber(sellAmount).comparedTo(new BigNumber(balance || 0)) > 0) {
      errorMsg('Insufficient balance');
      return;
    }

    setIsLoading(true);
    try {
      const latestBlockhash = await connection.getLatestBlockhash();

      const func = sdk.sellTx(
        new PublicKey(coinAddress),
        new PublicKey(config.feeRecipient),
        new BN(convertDecToMist(sellAmount, 6)),
        new BN('0'),
      );
      const transaction = await func.transaction();
      transaction.recentBlockhash = latestBlockhash.blockhash;
      transaction.feePayer = new PublicKey(activeWallet?.address || '');

      // 🔹 Sign with Solflare
      const signedTx = await activeWallet?.signTransaction(transaction);
      const rawTransaction = signedTx.serialize();

      // 🔹 Send Transaction
      const txid = await connection.sendRawTransaction(rawTransaction, {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
      });
      console.log('🔄 Transaction sent! Waiting for confirmation...');

      // Wait for transaction confirmation
      const confirmation = await connection.confirmTransaction(
        {
          signature: txid,
          blockhash: latestBlockhash.blockhash,
          lastValidBlockHeight: latestBlockhash.lastValidBlockHeight,
        },
        'confirmed',
      );

      if (confirmation.value.err) {
        throw new Error(
          `Transaction failed: ${JSON.stringify(confirmation.value.err)}`,
        );
      }
      successMsg('Sell successfully!', txid);
      console.log('✅ Transaction confirmed! Hash:', txid);
    } catch (error) {
      errorMsg('Sell failed!');
      console.log('💔💔💔💔💔--------> error', error);
    } finally {
      setIsLoading(false);
      setSellAmount('');
      setEstimateAmountOut('');
      setTimeout(() => {
        fetchBalances();
      }, 2000);
    }
  };

  return (
    <div className="flex-1">
      <div className="desktop:border border-white-50 rounded-[4px]">
        <div className="flex justify-between px-2 desktop:bg-transparent bg-black-900 desktop:py-4 py-2 rounded-[4px] gap-2">
          <div className="body-sm-light-12 text-white-800">Amount</div>
          <div className="flex gap-2 flex-1 items-center">
            <NumericFormat
              value={sellAmount}
              onValueChange={(values) => setSellAmount(values.value)}
              thousandSeparator=","
              decimalSeparator="."
              allowNegative={false}
              placeholder="0.00"
              className="font-bold text-right bg-transparent w-full appearance-none focus:outline-none body-md-regular-14"
              allowLeadingZeros={false}
              decimalScale={SOL_DECIMALS}
              inputMode="numeric"
            />
            <Image
              src={coin?.logoUri as string}
              alt={coin.symbol}
              width={14}
              height={14}
              className="w-14px h-14px object-cover rounded-full"
            />
          </div>
        </div>
        <div className="desktop:border-t border-white-50 grid grid-cols-4 mt-2 desktop:mt-0 desktop:gap-0 gap-1">
          {PERCENT_SELL.map((item, index) => (
            <div
              key={item}
              className={`border desktop:border-0 ${
                index === PERCENT_SELL.length - 1 ? '' : 'desktop:border-r '
              } desktop:rounded-none rounded-[4px] border-white-50 p-1 text-white-800 body-md-light-14 text-center cursor-pointer hover:bg-white-100`}
              onClick={() => {
                if (!balance) return;
                setPercentageOfBalance(balance.toString(), item);
              }}
            >
              {item === 100 ? 'max' : `${item}%`}
            </div>
          ))}
        </div>
      </div>

      <div className="flex w-full cursor-pointer items-center justify-between my-3 gap-2">
        <div className="flex gap-2">
          <div className="flex items-center gap-2 text-white-500">
            <CoinTip /> <p className="body-sm-regular-12">0.01</p>
          </div>
          <div className="flex items-center gap-2 text-white-500">
            <SlippageIcon /> <p className="body-sm-regular-12">{slippage}%</p>
          </div>
        </div>

        <AppButton
          className="flex desktop:hidden justify-center h-[40px] !rounded-[4px] !gap-1 flex-1"
          onClick={handleSellCoin}
          disabled={!sdk || !sellAmount || !balance}
          isLoading={isLoading}
          type="submit"
          buttonType="contained-brand"
        >
          <FlashIcon /> Sell
        </AppButton>

        <div
          className="flex items-center gap-2 text-white-500 desktop:p-0 desktop:border-0 p-2 border border-white-50 rounded-[4px]"
          onClick={() => setIsOpenModalSettingSlippage(true)}
        >
          <SettingIcon />{' '}
          <p className="body-sm-regular-12 desktop:block hidden">Settings</p>
        </div>
      </div>

      <AppButton
        className="desktop:flex hidden w-full justify-center h-[48px] !rounded-[4px] !gap-1"
        onClick={handleSellCoin}
        disabled={!sdk || !sellAmount || !balance || isCurveComplete}
        isButtonAuth
        classNameBtnAuth="w-full"
        isLoading={isLoading}
        type="submit"
        buttonType="contained-brand"
      >
        <FlashIcon /> {isCurveComplete ? 'Migrating to dex' : 'Sell'}
      </AppButton>

      {isOpenModalSettingSlippage && (
        <ModalSettingSlippage
          isOpen={isOpenModalSettingSlippage}
          onClose={() => setIsOpenModalSettingSlippage(false)}
          slippage={slippage}
          setSlippage={setSlippage}
        />
      )}
    </div>
  );
};

export default SellOrderForm;
