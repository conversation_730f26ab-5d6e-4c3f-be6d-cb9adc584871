import {
  Cart2Icon,
  CartIcon,
  Tag2Icon,
  TagIcon,
  WalletUserIcon,
} from '@/assets/icons';
import BuyOrderForm from '@/components/orderform/BuyOrderForm';
import { useBalance } from '@/hooks';
import clsx from '@/node_modules/clsx';
import Image from '@/node_modules/next/image';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { formatNumberWithCommas } from '@/utils/format';
import { useState } from 'react';
import SellOrderForm from './SellOrderForm';
import { NetworkSwitcher } from '@/components/NetworkSwitcher';
enum SWAP_TYPE {
  BUY = 'BUY',
  SELL = 'SELL',
}

export const OrderForm = () => {
  const [orderType, setOrderType] = useState<SWAP_TYPE>(SWAP_TYPE.BUY);
  const { coin, coinAddress } = useCoinPageContext();
  const { ethBalance, getBalanceByTokenAddress } = useBalance();

  const BUTTONS = [
    {
      value: SWAP_TYPE.BUY,
      label: 'Buy',
      iconActive: <CartIcon />,
      iconInActive: <Cart2Icon />,
    },
    {
      value: SWAP_TYPE.SELL,
      label: 'Sell',
      iconActive: <TagIcon />,
      iconInActive: <Tag2Icon />,
    },
  ];

  const _renderForm = () => (
    <>
      <NetworkSwitcher />
      <div className="flex desktop:block w-ful gap-4">
        <div className="w-full">
          <div className="flex justify-between mb-3">
            <div className="body-sm-regular-12 text-white-500 flex gap-1 items-center">
              <WalletUserIcon />
              Your balance
            </div>

            <div className="body-sm-regular-12 flex gap-1 items-center">
              {formatNumberWithCommas(
                orderType === SWAP_TYPE.BUY
                  ? ethBalance!
                  : getBalanceByTokenAddress(coinAddress),
              )}{' '}
              {orderType === SWAP_TYPE.BUY ? (
                <span className="text-white-500 text-sm">ETH</span>
              ) : (
                <Image
                  src={coin?.logoUri as string}
                  alt={coin.symbol}
                  width={14}
                  height={14}
                  className="w-14px h-14px object-cover rounded-full"
                />
              )}
            </div>
          </div>
          <div className="hidden desktop:flex gap-1 mb-3 border border-white-50 p-1 rounded-[4px]">
            {BUTTONS.map((item) => {
              const isActive = orderType === item?.value;
              return (
                <button
                  key={item?.value}
                  className={clsx(
                    'flex items-center justify-center gap-2 w-1/2 h-[32px] body-md-regular-14 rounded-[4px]',
                    isActive
                      ? 'bg-white-100 text-white-1000'
                      : 'text-white-500',
                  )}
                  onClick={() => setOrderType(item?.value)}
                >
                  {isActive ? item?.iconActive : item?.iconInActive}
                  {item?.label}
                </button>
              );
            })}
          </div>

          {orderType === SWAP_TYPE.BUY ? <BuyOrderForm /> : <SellOrderForm />}
        </div>

        <div
          onClick={() => {
            if (orderType === SWAP_TYPE.SELL) {
              setOrderType(SWAP_TYPE.BUY);
              return;
            }
            setOrderType(SWAP_TYPE.SELL);
          }}
          className="desktop:hidden p-2 flex gap-2 items-center justify-center bg-white-100 rotate-180 text-center rounded-tr-md rounded-br-md body-sm-regular-12"
          style={{ writingMode: 'vertical-rl' }}
        >
          {orderType === SWAP_TYPE.BUY ? (
            <TagIcon className="rotate-90" />
          ) : (
            <CartIcon className="rotate-90" />
          )}
          {orderType === SWAP_TYPE.BUY ? 'Sell' : 'Buy'}
        </div>
      </div>
    </>
  );

  return (
    <div className="desktop:p-4 pl-2 pt-2 pb-4 border-b border-white-50">
      {_renderForm()}
    </div>
  );
};
