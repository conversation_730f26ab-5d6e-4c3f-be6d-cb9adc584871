'use client';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { CoinTip, FlashIcon, SettingIcon, SlippageIcon } from '@/assets/icons';
import { AppButton } from '@/components/AppButton';
import { ETH_DECIMALS } from '@/constants';
import { useBalance } from '@/hooks';
import { useHyperliquidSDK } from '@/hooks/useHyperliquidSDK';
import { errorMsg, successMsg } from '@/libs/toast';
import { ModalSettingSlippage } from '@/modals';
import BigNumber from 'bignumber.js';
import { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';

const BUY_AMOUNT = [0.01, 0.02, 0.5, 1];

const BuyOrderForm = () => {
  const { sdk, activeWallet, provider } = useHyperliquidSDK();

  const { ethBalance, fetchBalances } = useBalance();
  const { coinAddress, isCurveComplete } = useCoinPageContext();

  const [amountIn, setAmountIn] = useState<string>('');
  const [isOpenModalSettingSlippage, setIsOpenModalSettingSlippage] =
    useState<boolean>(false);
  const [slippage, setSlippage] = useState<number>(5.0);
  const [isLoading, setIsLoading] = useState(false);
  const [estimatedAmountOut, setEstimateAmountOut] = useState('');

  // Display estimated tokens to be received
  const displayEstimatedTokens = estimatedAmountOut
    ? `≈ ${estimatedAmountOut} tokens`
    : '';

  useEffect(() => {
    setEstimateAmountOut('');

    if (!amountIn || !sdk || isCurveComplete) {
      return;
    }

    const getEstimateAmountOut = async () => {
      try {
        const estimatedTokens = await sdk.getBuyPrice(coinAddress, amountIn);
        if (Number(estimatedTokens) > 0) setEstimateAmountOut(estimatedTokens);
      } catch (error) {
        console.error('Error getting buy price:', error);
      }
    };
    getEstimateAmountOut();
  }, [amountIn, sdk, coinAddress, isCurveComplete]);

  const handleBuyCoin = async () => {
    if (!sdk) return;

    if (Number(amountIn) <= 0) {
      errorMsg('Amount must be greater than 0');
      return;
    }

    if (
      new BigNumber(amountIn).comparedTo(new BigNumber(ethBalance || 0)) > 0
    ) {
      errorMsg('Insufficient balance');
      return;
    }
    setIsLoading(true);
    try {
      if (!provider || !activeWallet) {
        throw new Error('Provider or wallet not available');
      }

      // Buy tokens through bonding curve
      const txResponse = await sdk.buyTokens({
        tokenAddress: coinAddress,
        ethAmount: amountIn,
        slippage: slippage,
      });

      console.log('🔄 Transaction sent! Waiting for confirmation...');
      console.log('Transaction ID:', txResponse);

      successMsg('Buy successfully!', txResponse);
      console.log('✅ Transaction confirmed! Hash:', txResponse);
    } catch (error) {
      errorMsg(String(error) || 'Buy failed!');
      console.log('💔💔💔💔💔--------> error', error);
    } finally {
      setIsLoading(false);
      setAmountIn('');
      setEstimateAmountOut('');
      setTimeout(() => {
        fetchBalances();
      }, 2000);
    }
  };

  return (
    <div className="flex-1">
      <div className="desktop:border border-white-50 rounded-[4px]">
        <div className="flex justify-between px-2 desktop:bg-transparent bg-black-900 desktop:py-4 py-2 rounded-[4px] gap-2">
          <div className="body-sm-light-12 text-white-800">Amount</div>
          <div className="flex gap-2 flex-1 items-center">
            <NumericFormat
              value={amountIn}
              onValueChange={(values) => setAmountIn(values.value)}
              thousandSeparator=","
              decimalSeparator="."
              allowNegative={false}
              placeholder="0.00"
              className="font-bold text-right bg-transparent w-full appearance-none focus:outline-none body-md-regular-14"
              allowLeadingZeros={false}
              decimalScale={ETH_DECIMALS}
              inputMode="numeric"
            />
            <span className="text-white-500 text-sm">ETH</span>
          </div>
        </div>
        <div className="desktop:border-t border-white-50 grid grid-cols-4 mt-2 desktop:mt-0 desktop:gap-0 gap-1">
          {BUY_AMOUNT.map((item, index) => (
            <div
              key={item}
              className={`border desktop:border-0 ${
                index === BUY_AMOUNT.length - 1 ? '' : 'desktop:border-r '
              } desktop:rounded-none rounded-[4px] border-white-50 p-1 text-white-800 body-md-light-14 text-center cursor-pointer hover:bg-white-100`}
              onClick={() => setAmountIn(item.toString())}
            >
              {item}
            </div>
          ))}
        </div>
      </div>

      {displayEstimatedTokens && (
        <div className="text-center text-white-500 text-sm my-2">
          {displayEstimatedTokens}
        </div>
      )}

      <div className="flex w-full cursor-pointer items-center justify-between my-3 gap-2">
        <div className="flex gap-2">
          <div className="flex items-center gap-2 text-white-500">
            <CoinTip /> <p className="body-sm-regular-12">0.01</p>
          </div>
          <div className="flex items-center gap-2 text-white-500">
            <SlippageIcon /> <p className="body-sm-regular-12">{slippage}%</p>
          </div>
        </div>

        <AppButton
          className="w-full flex desktop:hidden justify-center h-[40px] !rounded-[4px] !gap-1"
          type="submit"
          buttonType="contained-brand"
          onClick={handleBuyCoin}
          isLoading={isLoading}
          disabled={!sdk}
        >
          <FlashIcon /> Buy
        </AppButton>

        <div
          className="flex items-center gap-2 text-white-500 desktop:p-0 desktop:border-0 p-2 border border-white-50 rounded-[4px]"
          onClick={() => setIsOpenModalSettingSlippage(true)}
        >
          <SettingIcon />{' '}
          <p className="body-sm-regular-12 desktop:block hidden">Settings</p>
        </div>
      </div>

      <AppButton
        className="w-full hidden desktop:flex justify-center h-[48px] !rounded-[4px] !gap-1"
        type="submit"
        buttonType="contained-brand"
        onClick={handleBuyCoin}
        isLoading={isLoading}
        disabled={!sdk || !amountIn || !ethBalance || isCurveComplete}
        classNameBtnAuth="w-full"
        isButtonAuth
      >
        <FlashIcon /> {isCurveComplete ? 'Migrating to dex' : 'Buy'}
      </AppButton>

      {isOpenModalSettingSlippage && (
        <ModalSettingSlippage
          isOpen={isOpenModalSettingSlippage}
          onClose={() => setIsOpenModalSettingSlippage(false)}
          slippage={slippage}
          setSlippage={setSlippage}
        />
      )}
    </div>
  );
};

export default BuyOrderForm;
