'use client';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import {
  CoinTip,
  FlashIcon,
  SettingIcon,
  SlippageIcon,
  SolanaIcon,
} from '@/assets/icons';
import { AppButton } from '@/components/AppButton';
import config from '@/config';
import { SOL_DECIMALS } from '@/constants';
import { useBalance } from '@/hooks';
import { useBondingCurveSDK } from '@/hooks/useBondingCurveSDK';
import { errorMsg, successMsg } from '@/libs/toast';
import { ModalSettingSlippage } from '@/modals';
import { convertDecToMist } from '@/utils/format';
import { BN } from '@coral-xyz/anchor';
import { PublicKey } from '@solana/web3.js';
import BigNumber from 'bignumber.js';
import { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';

const BUY_AMOUNT = [0.01, 0.02, 0.5, 1];

const BuyOrderForm = () => {
  const { sdk, activeWallet, connection } = useBondingCurveSDK();

  const { solBalance, fetchBalances } = useBalance();
  const { coinAddress, isCurveComplete } = useCoinPageContext();

  const [amountIn, setAmountIn] = useState<string>('');
  const [isOpenModalSettingSlippage, setIsOpenModalSettingSlippage] =
    useState<boolean>(false);
  const [slippage, setSlippage] = useState<number>(5.0);
  const [isLoading, setIsLoading] = useState(false);
  const [estimatedAmountOut, setEstimateAmountOut] = useState('');

  useEffect(() => {
    setEstimateAmountOut('');

    if (!amountIn || !sdk || isCurveComplete) {
      return;
    }

    const getEstimateAmountOut = async () => {
      const res = await sdk.getBuyPrice(
        new PublicKey(coinAddress),
        new BN(convertDecToMist(amountIn)),
      );
      if (Number(res) > 0) setEstimateAmountOut(res.toString());
    };
    getEstimateAmountOut();
  }, [amountIn, sdk]);

  const handleBuyCoin = async () => {
    if (!sdk) return;

    if (Number(amountIn) <= 0) {
      errorMsg('Amount must be greater than 0');
      return;
    }

    if (
      new BigNumber(amountIn).comparedTo(new BigNumber(solBalance || 0)) > 0
    ) {
      errorMsg('Insufficient balance');
      return;
    }
    setIsLoading(true);
    try {
      const latestBlockhash = await connection.getLatestBlockhash();

      const func = await sdk.buyTx(
        new PublicKey(coinAddress),
        new PublicKey(config.feeRecipient),
        new BN(estimatedAmountOut),
        new BN('1000000000000000'),
      );

      const transaction = await func.transaction();
      transaction.recentBlockhash = latestBlockhash.blockhash;
      transaction.feePayer = new PublicKey(activeWallet?.address || '');

      // 🔹 Sign with Solflare
      const signedTx = await activeWallet?.signTransaction(transaction);
      const rawTransaction = signedTx.serialize();

      // 🔹 Send Transaction
      const txid = await connection.sendRawTransaction(rawTransaction, {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
      });
      console.log('🔄 Transaction sent! Waiting for confirmation...');

      // Wait for transaction confirmation
      const confirmation = await connection.confirmTransaction(
        {
          signature: txid,
          blockhash: latestBlockhash.blockhash,
          lastValidBlockHeight: latestBlockhash.lastValidBlockHeight,
        },
        'confirmed',
      );

      if (confirmation.value.err) {
        throw new Error(
          `Transaction failed: ${JSON.stringify(confirmation.value.err)}`,
        );
      }
      successMsg('Buy successfully!', txid);
      console.log('✅ Transaction confirmed! Hash:', txid);
    } catch (error) {
      errorMsg(String(error) || 'Buy failed!');
      console.log('💔💔💔💔💔--------> error', error);
    } finally {
      setIsLoading(false);
      setAmountIn('');
      setEstimateAmountOut('');
      setTimeout(() => {
        fetchBalances();
      }, 2000);
    }
  };

  return (
    <div className="flex-1">
      <div className="desktop:border border-white-50 rounded-[4px]">
        <div className="flex justify-between px-2 desktop:bg-transparent bg-black-900 desktop:py-4 py-2 rounded-[4px] gap-2">
          <div className="body-sm-light-12 text-white-800">Amount</div>
          <div className="flex gap-2 flex-1 items-center">
            <NumericFormat
              value={amountIn}
              onValueChange={(values) => setAmountIn(values.value)}
              thousandSeparator=","
              decimalSeparator="."
              allowNegative={false}
              placeholder="0.00"
              className="font-bold text-right bg-transparent w-full appearance-none focus:outline-none body-md-regular-14"
              allowLeadingZeros={false}
              decimalScale={SOL_DECIMALS}
              inputMode="numeric"
            />
            <SolanaIcon />
          </div>
        </div>
        <div className="desktop:border-t border-white-50 grid grid-cols-4 mt-2 desktop:mt-0 desktop:gap-0 gap-1">
          {BUY_AMOUNT.map((item, index) => (
            <div
              key={item}
              className={`border desktop:border-0 ${
                index === BUY_AMOUNT.length - 1 ? '' : 'desktop:border-r '
              } desktop:rounded-none rounded-[4px] border-white-50 p-1 text-white-800 body-md-light-14 text-center cursor-pointer hover:bg-white-100`}
              onClick={() => setAmountIn(item.toString())}
            >
              {item}
            </div>
          ))}
        </div>
      </div>

      <div className="flex w-full cursor-pointer items-center justify-between my-3 gap-2">
        <div className="flex gap-2">
          <div className="flex items-center gap-2 text-white-500">
            <CoinTip /> <p className="body-sm-regular-12">0.01</p>
          </div>
          <div className="flex items-center gap-2 text-white-500">
            <SlippageIcon /> <p className="body-sm-regular-12">{slippage}%</p>
          </div>
        </div>

        <AppButton
          className="w-full flex desktop:hidden justify-center h-[40px] !rounded-[4px] !gap-1"
          type="submit"
          buttonType="contained-brand"
          onClick={handleBuyCoin}
          isLoading={isLoading}
          disabled={!sdk}
        >
          <FlashIcon /> Buy
        </AppButton>

        <div
          className="flex items-center gap-2 text-white-500 desktop:p-0 desktop:border-0 p-2 border border-white-50 rounded-[4px]"
          onClick={() => setIsOpenModalSettingSlippage(true)}
        >
          <SettingIcon />{' '}
          <p className="body-sm-regular-12 desktop:block hidden">Settings</p>
        </div>
      </div>

      <AppButton
        className="w-full hidden desktop:flex justify-center h-[48px] !rounded-[4px] !gap-1"
        type="submit"
        buttonType="contained-brand"
        onClick={handleBuyCoin}
        isLoading={isLoading}
        disabled={!sdk || !amountIn || !solBalance || isCurveComplete}
        classNameBtnAuth="w-full"
        isButtonAuth
      >
        <FlashIcon /> {isCurveComplete ? 'Migrating to dex' : 'Buy'}
      </AppButton>

      {isOpenModalSettingSlippage && (
        <ModalSettingSlippage
          isOpen={isOpenModalSettingSlippage}
          onClose={() => setIsOpenModalSettingSlippage(false)}
          slippage={slippage}
          setSlippage={setSlippage}
        />
      )}
    </div>
  );
};

export default BuyOrderForm;
