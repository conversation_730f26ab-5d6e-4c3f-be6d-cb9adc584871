import AppAddress from '@/components/AppAddress';
import { AppTimeDisplay } from '@/components/AppTimeDisplay';
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from '@/libs/broadcast';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import rf from '@/services/RequestFactory';
import { TTrades } from '@/types';
import { formatNumber } from '@/utils/format';
import { getRandomAvatarUrl } from '@/utils/helper';
import clsx from 'clsx';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import AppTable, { IColumn } from '../AppTable';

const TransactionList = () => {
  const { coinAddress } = useCoinPageContext();
  const [page, setPage] = useState(1);
  const [tradeData, setTradeData] = useState<TTrades[]>([]);
  const [hasNextPage, setHasNextPage] = useState(true);

  const handleWhenTradeCreated = async (event: TBroadcastEvent) => {
    const data = JSON.parse(event?.detail);
    if (data?.tokenAddress === coinAddress) {
      setTradeData((prev) => [data, ...prev]);
    }
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.TRADES, handleWhenTradeCreated);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.TRADES, handleWhenTradeCreated);
    };
  }, []);

  const columns: IColumn<TTrades>[] = [
    {
      title: 'Account',
      key: 'maker',
      render: (_, value) => (
        <div>
          <div className="flex items-center gap-8px">
            <Image
              src={getRandomAvatarUrl()}
              alt="avatar"
              width={32}
              height={32}
              className="object-cover w-32px h-32px rounded-full"
            />
            <div className="flex items-center gap-8px">
              <AppAddress address={value?.maker} />
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Type',
      key: 'tradeType',
      render: (value) => (
        <p
          className={clsx(
            'text-body-sm font-normal',
            value === 'BUY' ? 'text-green-500' : 'text-red-500',
          )}
        >
          {value === 'BUY' ? 'Buy' : 'Sell'}
        </p>
      ),
    },
    {
      title: 'SOL',
      key: 'quoteAmount',
      render: (_, value) => (
        <p className="text-body-sm font-normal">
          {formatNumber(value.quoteAmount, 9, '0')}
        </p>
      ),
    },
    {
      title: 'Token',
      key: 'baseAmount',
      render: (_, value) => (
        <p className="text-body-sm font-normal">
          {formatNumber(value.baseAmount, 9, '0')}
        </p>
      ),
    },
    {
      title: 'Date',
      key: 'timestamp',
      render: (_, value) => (
        <div className="text-body-sm font-normal">
          <AppTimeDisplay timestamp={value?.timestamp * 1000} isAgo />
        </div>
      ),
    },
    {
      title: 'Transaction',
      key: 'hash',
      render: (_, value) => (
        <div className="flex items-center gap-8px">
          <AppAddress address={value.hash} />
        </div>
      ),
    },
  ];

  const handleNextPage = useCallback(() => {
    if (hasNextPage) {
      setPage((prev) => prev + 1);
    }
  }, [hasNextPage]);

  const handleGetTrades = useCallback(async () => {
    if (!coinAddress) return;
    try {
      await rf
        .getRequest('TradeRequest')
        .getTrades({
          page,
          limit: 20,
          tokenAddress: coinAddress || '',
        })
        .then((result) => {
          if (!!result) {
            const newDocs = result?.docs.map((item: any) => ({
              ...item,
              user: {
                ...item.user,
                fallbackLogoUrl: getRandomAvatarUrl(),
              },
            }));
            setTradeData((prev) =>
              newDocs ? (page === 1 ? newDocs : [...prev, ...newDocs]) : [],
            );
            if (page >= result?.totalPages) {
              setHasNextPage(false);
            }
          }
        });
    } catch (err) {
      console.error(err);
      setTradeData([]);
    }
  }, [page, coinAddress]);

  useEffect(() => {
    handleGetTrades();
  }, [handleGetTrades]);

  return (
    <div className="px-4 overflow-x-auto">
      <AppTable
        columns={columns}
        data={tradeData}
        loadMore={handleNextPage}
        noDataMessage="No Transaction"
      />
    </div>
  );
};

export default TransactionList;
