'use client';

import { useEffect, useState } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { AppButton } from './AppButton';
import { 
  isOnCorrectChain, 
  switchToHyperliquidChain, 
  getCurrentChainId, 
  onChainChanged,
  getTargetChain 
} from '@/utils/chainSwitcher';

export const ChainSwitcher = () => {
  const { authenticated } = usePrivy();
  const [isCorrectChain, setIsCorrectChain] = useState(false);
  const [currentChainId, setCurrentChainId] = useState<number | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const [isSwitching, setIsSwitching] = useState(false);

  const targetChain = getTargetChain();

  // Check chain on mount and when authentication changes
  useEffect(() => {
    const checkChain = async () => {
      if (!authenticated) {
        setIsChecking(false);
        return;
      }

      try {
        const [isCorrect, chainId] = await Promise.all([
          isOnCorrectChain(),
          getCurrentChainId(),
        ]);
        
        setIsCorrectChain(isCorrect);
        setCurrentChainId(chainId);
      } catch (error) {
        console.error('Error checking chain:', error);
        setIsCorrectChain(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkChain();
  }, [authenticated]);

  // Listen for chain changes
  useEffect(() => {
    if (!authenticated) return;

    const cleanup = onChainChanged(async (chainId) => {
      setCurrentChainId(chainId);
      const isCorrect = await isOnCorrectChain();
      setIsCorrectChain(isCorrect);
    });

    return cleanup;
  }, [authenticated]);

  const handleSwitchChain = async () => {
    setIsSwitching(true);
    try {
      await switchToHyperliquidChain();
      // Chain change will be detected by the listener
    } catch (error) {
      console.error('Error switching chain:', error);
    } finally {
      setIsSwitching(false);
    }
  };

  // Don't show anything if not authenticated
  if (!authenticated) {
    return null;
  }

  // Show loading state
  if (isChecking) {
    return (
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-4">
        <p className="text-blue-400 text-sm">Checking network...</p>
      </div>
    );
  }

  // Show success state if on correct chain
  if (isCorrectChain) {
    return (
      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <p className="text-green-400 text-sm">
            Connected to {targetChain.chainName}
          </p>
        </div>
      </div>
    );
  }

  // Show switch network prompt
  return (
    <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between gap-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            <p className="text-orange-400 text-sm font-medium">Wrong Network</p>
          </div>
          <p className="text-orange-300 text-xs">
            Please switch to {targetChain.chainName} to use this app
          </p>
          {currentChainId && (
            <p className="text-orange-200 text-xs mt-1">
              Current: Chain ID {currentChainId}
            </p>
          )}
        </div>
        <AppButton
          onClick={handleSwitchChain}
          isLoading={isSwitching}
          className="text-xs px-3 py-2 whitespace-nowrap"
          buttonType="contained-brand"
        >
          {isSwitching ? 'Switching...' : 'Switch Network'}
        </AppButton>
      </div>
    </div>
  );
};
