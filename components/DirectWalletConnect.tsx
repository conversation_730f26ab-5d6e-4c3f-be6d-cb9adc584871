'use client';

import { useDirectWallet } from '@/hooks/useDirectWallet';
import { AppButton } from './AppButton';
import { copyToClipboard } from '@/utils/helper';
import { useState } from 'react';

export const DirectWalletConnect = () => {
  const {
    isConnected,
    address,
    chainId,
    isConnecting,
    connectWallet,
    disconnectWallet,
  } = useDirectWallet();

  const [showTooltip, setShowTooltip] = useState(false);

  const handleCopyAddress = () => {
    if (address) {
      copyToClipboard(address);
      setShowTooltip(true);
      setTimeout(() => setShowTooltip(false), 2000);
    }
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getChainName = (id: number) => {
    switch (id) {
      case 1:
        return 'Ethereum';
      case 11155111:
        return 'Sepolia';
      case 998:
        return 'Hyperliquid Testnet';
      case 42161:
        return 'Hyperliquid';
      default:
        return `Chain ${id}`;
    }
  };

  if (!isConnected) {
    return (
      <div className="flex flex-col items-center gap-4 p-6 border border-white-50 rounded-lg">
        <h3 className="text-lg font-medium">Connect Your Wallet</h3>
        <p className="text-white-500 text-sm text-center">
          Connect your Ethereum wallet to start trading meme coins
        </p>
        <AppButton
          onClick={connectWallet}
          isLoading={isConnecting}
          buttonType="contained-brand"
          className="w-full"
        >
          {isConnecting ? 'Connecting...' : 'Connect Wallet'}
        </AppButton>
        <p className="text-xs text-white-400 text-center">
          Make sure you have MetaMask or another Ethereum wallet installed
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3 p-4 border border-white-50 rounded-lg">
      <div className="flex items-center justify-between">
        <span className="text-sm text-white-500">Wallet</span>
        <AppButton
          onClick={disconnectWallet}
          buttonType="outlined-grey"
          className="text-xs px-2 py-1"
        >
          Disconnect
        </AppButton>
      </div>

      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        <span className="text-sm font-mono">{formatAddress(address!)}</span>
        <button
          onClick={handleCopyAddress}
          className="text-white-500 hover:text-white-300 text-xs"
          title="Copy address"
        >
          📋
        </button>
        {showTooltip && <span className="text-xs text-green-500">Copied!</span>}
      </div>

      <div className="flex items-center justify-between text-xs">
        <span className="text-white-500">Network:</span>
        <span
          className={`px-2 py-1 rounded text-xs ${
            chainId === 998 || chainId === 42161
              ? 'bg-green-500/20 text-green-500'
              : 'bg-orange-500/20 text-orange-500'
          }`}
        >
          {getChainName(chainId!)}
        </span>
      </div>

      {chainId !== 998 && chainId !== 42161 && (
        <div className="text-xs text-orange-500 bg-orange-500/10 p-2 rounded">
          ⚠️ Please switch to Hyperliquid network for full functionality
        </div>
      )}
    </div>
  );
};
