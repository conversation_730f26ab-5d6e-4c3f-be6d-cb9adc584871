'use client';

import { useState, useEffect } from 'react';
import { usePrivy, useWallets } from '@privy-io/react-auth';
import { ethers } from 'ethers';
import { AppButton } from './AppButton';
import { isOnCorrectChain } from '@/utils/chainSwitcher';

export const TestWalletFunctions = () => {
  const { authenticated } = usePrivy();
  const { wallets } = useWallets();
  const [balance, setBalance] = useState<string>('0');
  const [isLoading, setIsLoading] = useState(false);
  const [transferAmount, setTransferAmount] = useState('0.001');
  const [recipientAddress, setRecipientAddress] = useState('');
  const [txHash, setTxHash] = useState('');
  const [error, setError] = useState('');
  const [isCorrectChain, setIsCorrectChain] = useState(false);

  // Get the active wallet
  const activeWallet = wallets.find(wallet => wallet.walletClientType === 'privy');

  // Check chain on mount
  useEffect(() => {
    const checkChain = async () => {
      const correct = await isOnCorrectChain();
      setIsCorrectChain(correct);
    };
    
    if (authenticated) {
      checkChain();
    }
  }, [authenticated]);

  // Get balance function
  const getBalance = async () => {
    if (!activeWallet || !authenticated || !isCorrectChain) {
      setError('Please connect wallet and switch to correct network');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Get provider from Privy wallet
      const provider = await activeWallet.getEthereumProvider();
      const ethersProvider = new ethers.BrowserProvider(provider);
      const signer = await ethersProvider.getSigner();
      const address = await signer.getAddress();
      
      // Get balance
      const balanceWei = await ethersProvider.getBalance(address);
      const balanceEth = ethers.formatEther(balanceWei);
      
      setBalance(balanceEth);
      console.log('Balance:', balanceEth, 'ETH');
    } catch (err: any) {
      console.error('Error getting balance:', err);
      setError(`Error getting balance: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Transfer function
  const transferETH = async () => {
    if (!activeWallet || !authenticated || !isCorrectChain) {
      setError('Please connect wallet and switch to correct network');
      return;
    }

    if (!recipientAddress || !ethers.isAddress(recipientAddress)) {
      setError('Please enter a valid recipient address');
      return;
    }

    if (!transferAmount || parseFloat(transferAmount) <= 0) {
      setError('Please enter a valid transfer amount');
      return;
    }

    setIsLoading(true);
    setError('');
    setTxHash('');

    try {
      // Get provider and signer from Privy wallet
      const provider = await activeWallet.getEthereumProvider();
      const ethersProvider = new ethers.BrowserProvider(provider);
      const signer = await ethersProvider.getSigner();
      
      // Create transaction
      const tx = {
        to: recipientAddress,
        value: ethers.parseEther(transferAmount),
      };

      // Send transaction
      const txResponse = await signer.sendTransaction(tx);
      setTxHash(txResponse.hash);
      
      console.log('Transaction sent:', txResponse.hash);
      
      // Wait for confirmation
      const receipt = await txResponse.wait();
      console.log('Transaction confirmed:', receipt);
      
      // Refresh balance
      await getBalance();
      
    } catch (err: any) {
      console.error('Error sending transaction:', err);
      setError(`Error sending transaction: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-load balance when wallet connects
  useEffect(() => {
    if (authenticated && activeWallet && isCorrectChain) {
      getBalance();
    }
  }, [authenticated, activeWallet, isCorrectChain]);

  if (!authenticated) {
    return (
      <div className="bg-gray-500/10 border border-gray-500/20 rounded-lg p-4">
        <p className="text-gray-400 text-sm">Please connect your wallet to test functions</p>
      </div>
    );
  }

  if (!isCorrectChain) {
    return (
      <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
        <p className="text-orange-400 text-sm">Please switch to Hyperliquid network to test functions</p>
      </div>
    );
  }

  return (
    <div className="bg-white-10 border border-white-50 rounded-lg p-6 space-y-6">
      <h3 className="text-lg font-medium text-white">Test Wallet Functions</h3>
      
      {/* Balance Section */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-white-700">ETH Balance</h4>
          <AppButton
            onClick={getBalance}
            isLoading={isLoading}
            buttonType="outlined-grey"
            className="text-xs px-3 py-1"
          >
            Refresh
          </AppButton>
        </div>
        <div className="bg-gray-500/10 rounded p-3">
          <p className="text-white font-mono text-lg">{balance} ETH</p>
        </div>
      </div>

      {/* Transfer Section */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-white-700">Transfer ETH</h4>
        
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-white-500 mb-1">Recipient Address</label>
            <input
              type="text"
              value={recipientAddress}
              onChange={(e) => setRecipientAddress(e.target.value)}
              placeholder="0x..."
              className="w-full bg-gray-500/10 border border-white-50 rounded px-3 py-2 text-white text-sm font-mono"
            />
          </div>
          
          <div>
            <label className="block text-xs text-white-500 mb-1">Amount (ETH)</label>
            <input
              type="number"
              value={transferAmount}
              onChange={(e) => setTransferAmount(e.target.value)}
              step="0.001"
              min="0"
              className="w-full bg-gray-500/10 border border-white-50 rounded px-3 py-2 text-white text-sm"
            />
          </div>
          
          <AppButton
            onClick={transferETH}
            isLoading={isLoading}
            buttonType="contained-brand"
            className="w-full"
            disabled={!recipientAddress || !transferAmount}
          >
            {isLoading ? 'Sending...' : 'Send ETH'}
          </AppButton>
        </div>
      </div>

      {/* Transaction Hash */}
      {txHash && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-white-700">Transaction Hash</h4>
          <div className="bg-green-500/10 border border-green-500/20 rounded p-3">
            <p className="text-green-400 text-xs font-mono break-all">{txHash}</p>
            <a
              href={`https://app.hyperliquid.xyz/explorer/tx/${txHash}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-green-300 text-xs underline mt-1 inline-block"
            >
              View on Explorer
            </a>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded p-3">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {/* Wallet Info */}
      <div className="text-xs text-white-500 space-y-1">
        <p>Wallet: {activeWallet?.address}</p>
        <p>Type: {activeWallet?.walletClientType}</p>
      </div>
    </div>
  );
};
