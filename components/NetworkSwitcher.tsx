'use client';

import { useEffect, useState } from 'react';
import { useHyperliquidSDK } from '@/hooks/useHyperliquidSDK';
import { AppButton } from './AppButton';

export const NetworkSwitcher = () => {
  const { sdk } = useHyperliquidSDK();
  const [isOnHyperliquid, setIsOnHyperliquid] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [isSwitching, setIsSwitching] = useState(false);

  useEffect(() => {
    const checkNetwork = async () => {
      if (!sdk) return;
      
      try {
        const onHyperliquid = await sdk.isOnHyperliquid();
        setIsOnHyperliquid(onHyperliquid);
      } catch (error) {
        console.error('Error checking network:', error);
        setIsOnHyperliquid(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkNetwork();
  }, [sdk]);

  const handleSwitchNetwork = async () => {
    if (!sdk) return;

    setIsSwitching(true);
    try {
      const success = await sdk.switchToHyperliquid();
      if (success) {
        setIsOnHyperliquid(true);
      }
    } catch (error) {
      console.error('Error switching network:', error);
    } finally {
      setIsSwitching(false);
    }
  };

  if (isChecking) {
    return (
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-4">
        <p className="text-yellow-500 text-sm">Checking network...</p>
      </div>
    );
  }

  if (isOnHyperliquid) {
    return (
      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-4">
        <p className="text-green-500 text-sm">✅ Connected to Hyperliquid Network</p>
      </div>
    );
  }

  return (
    <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-orange-500 text-sm font-medium">Wrong Network</p>
          <p className="text-orange-400 text-xs mt-1">
            Please switch to Hyperliquid network to use this app
          </p>
        </div>
        <AppButton
          onClick={handleSwitchNetwork}
          isLoading={isSwitching}
          className="text-xs px-3 py-1"
          buttonType="contained-brand"
        >
          Switch Network
        </AppButton>
      </div>
    </div>
  );
};
