import dev from './dev.json';
import local from './local.json';
import prod from './prod.json';

export interface Config {
  apiUrl: string;
  recaptchaSiteKey: string;
  explorerUrl: string;
  network: string;
  feeRecipient: string;
  endpoints: {
    ws: string;
  };
  link: {
    twitter: string;
    telegram: string;
  };
  hyperliquidRpcUrl: string;
  chainId: number;
  privyAppId: string;
}

export const envConfig = process.env.NEXT_PUBLIC_ENV || 'dev';

interface EnvConfig {
  prod: Config;
  dev: Config;
  local: Config;
}

const configs: EnvConfig = { dev, prod, local } as EnvConfig;
const config: Config = configs[envConfig as keyof typeof configs];

export default config;
